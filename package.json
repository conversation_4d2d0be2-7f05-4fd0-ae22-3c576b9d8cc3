{"name": "amazon-ppc-analyzer", "version": "0.1.0", "private": true, "engines": {"node": "22.12.0"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.0.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "ajv": "^8.17.1", "autoprefixer": "^10.4.16", "chart.js": "4.4.1", "postcss": "^8.4.32", "react": "^18.2.0", "react-chartjs-2": "5.2.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-redux": "^9.0.4", "react-router-dom": "^5.3.3", "react-scripts": "^5.0.1", "recharts": "^2.14.1", "serve": "^14.2.1", "tailwindcss": "^3.4.0", "typescript": "^4.9.5", "xlsx": "^0.18.5"}, "scripts": {"dev": "react-scripts start", "build": "CI=false react-scripts build", "start": "serve -s build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/chart.js": "^2.9.41", "@types/jest": "^29.5.11", "@types/react-chartjs-2": "^2.0.2"}, "description": "A powerful React-based web application for analyzing Amazon PPC campaign data, providing actionable insights and optimization recommendations.", "main": "tailwind.config.js", "keywords": [], "author": "", "license": "ISC"}