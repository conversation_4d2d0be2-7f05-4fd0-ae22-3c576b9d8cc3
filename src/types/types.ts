export interface BaseReport {
  campaignName: string;
  customerSearchTerm?: string;
  impressions: number;
  clicks: number;
  spend: number;
  sevenDayTotalSales: number;
  sevenDayTotalOrders: number;
  startDate: string;
  endDate: string;
  cpc: number;
  type?: 'sponsored_products' | 'sponsored_brands';
}

export interface CampaignMetrics {
  name: string;
  campaignName: string;
  impressions: number;
  clicks: number;
  spend: number;
  sales: number;
  orders: number;
  acos?: number;
  roas?: number;
  ctr?: number;
  cvr?: number;
}
