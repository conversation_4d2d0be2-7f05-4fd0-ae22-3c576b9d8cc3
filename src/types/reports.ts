export interface BaseReport {
  startDate: string;
  endDate: string;
  portfolioName: string;
  currency: string;
  campaignName: string;
  adGroupName: string;
  targeting: string;
  matchType: string;
  customerSearchTerm: string;
  impressions: number;
  clicks: number;
  ctr: number;
  cpc: number;
  spend: number;
  sevenDayTotalSales: number;
  acos: number;
  roas: number;
  sevenDayTotalOrders: number;
  sevenDayTotalUnits: number;
  sevenDayConversionRate: number;
  sevenDayAdvertisedSkuUnits: number;
  sevenDayOtherSkuUnits: number;
  sevenDayAdvertisedSkuSales: number;
  sevenDayOtherSkuSales: number;
}

export interface SponsoredBrandsAttributedPurchasesReport extends BaseReport {
  attributionType: string;
  purchasedAsin: string;
  totalOrders: number;
  totalUnits: number;
}

export interface SponsoredBrandsKeywordReport extends BaseReport {
  keyword: string;
  matchType: string;
  adGroupName: string;
  sevenDayTotalOrders: number;
}

export interface SponsoredBrandsSearchTermReport extends BaseReport {
  searchTerm: string;
  matchType: string;
  adGroupName: string;
  conversionRate: number;
  sevenDayTotalOrders: number;
}

export interface SponsoredBrandsPlacementReport extends BaseReport {
  placementType: string;
  sevenDayTotalOrders: number;
}

export interface SponsoredBrandsImpressionShareReport extends BaseReport {
  searchTerm: string;
  impressionShare: number;
}

export interface SearchTermReport extends BaseReport {}

export interface PlacementReport extends BaseReport {
  placement: string;
}

export interface TargetingReport extends BaseReport {}

export interface AdvertisedProductReport extends BaseReport {
  advertisedSku: string;
  advertisedAsin: string;
}

export interface ProcessedReport {
  type: ReportType;
  data: BaseReport[];
}

export type ReportType = 
  | 'searchTerm' 
  | 'placement' 
  | 'targeting' 
  | 'advertisedProduct'
  | 'sponsoredBrandsAttributedPurchases'
  | 'sponsoredBrandsKeyword'
  | 'sponsoredBrandsSearchTerm'
  | 'sponsoredBrandsPlacement'
  | 'sponsoredBrandsImpressionShare';
