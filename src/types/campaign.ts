export interface CampaignMetrics {
  overallAcos: number;
  overallRoas: number;
  averageCpc: number;
  conversionRate: number;
  ctr: number;
  totalSpend: number;
  totalSales: number;
  spendTrend: number;
  salesTrend: number;
  acosTrend: number;
  roasTrend: number;
  wastedSpend: number;
  wastedAcos: number;
  totalOrders: number;
}

export interface Campaign {
  campaignId: string;
  campaignName: string;
  name: string;
  spend: number;
  sales: number;
  impressions: number;
  clicks: number;
  orders: number;
  acos: number;
  roas: number;
  ctr: number;
  conversionRate: number;
  costPerClick: number;
  costPerOrder: number;
  averageOrderValue: number;
  targeting?: string;
}

export interface CampaignPerformance {
  name: string;
  impressions: number;
  clicks: number;
  spend: number;
  sevenDayTotalSales: number;
  sevenDayTotalOrders: number;
  acos: number;
  roas: number;
}
