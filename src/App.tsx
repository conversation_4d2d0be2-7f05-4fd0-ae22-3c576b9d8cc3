import React, { useState } from 'react';
import { Route, Switch, Redirect, Link, NavLink } from 'react-router-dom';
import Banner from './components/Banner';
import { useSelector } from 'react-redux';
import { RootState } from './store/store';
import Dashboard from './components/Dashboard';
import CampaignAnalysis from './components/CampaignAnalysis';
import FileUpload from './components/FileUpload';
import LoadingOverlay from './components/LoadingOverlay';
import SearchTermsView from './components/SearchTermsView';
import WastedSpendAnalysis from './components/WastedSpendAnalysis';
import OpportunitiesPanel from './components/OpportunitiesPanel';

const App: React.FC = () => {
  const [showSearchTermsOnly, setShowSearchTermsOnly] = useState(false);
  
  const { loading, hasData, activeReportType } = useSelector((state: RootState) => state.campaign);

  return (
    <div className="min-h-screen bg-gray-100">
      <Banner />
      {loading && <LoadingOverlay />}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative flex h-16">
            <div className="absolute left-0 flex items-center h-full">
              <Link to="/" className="flex items-center">
                <img src={`${process.env.PUBLIC_URL}/logo-ppc-analyzer.png`} alt="PPC Analyzer" className="h-8 w-auto" />
              </Link>
            </div>
            <div className="flex-1 flex justify-center">
              <div className="hidden sm:flex sm:space-x-8 items-center">
                <NavLink
                  to="/"
                  exact
                  className="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  activeClassName="border-indigo-500 text-gray-900"
                >
                  Dashboard
                </NavLink>
                <NavLink
                  to="/campaign-analysis"
                  className="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  activeClassName="border-indigo-500 text-gray-900"
                >
                  Campaign Analysis
                </NavLink>
                <NavLink
                  to="/search-terms"
                  className="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  activeClassName="border-indigo-500 text-gray-900"
                >
                  Search Terms
                </NavLink>
                <NavLink
                  to="/opportunities"
                  className="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  activeClassName="border-indigo-500 text-gray-900"
                >
                  Opportunities
                </NavLink>
                <NavLink
                  to="/wasted-spend"
                  className="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  activeClassName="border-indigo-500 text-gray-900"
                >
                  Wasted Spend
                </NavLink>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Switch>
            <Route 
              exact 
              path="/" 
              render={() => hasData ? (
                <Dashboard 
                  showSearchTermsOnly={showSearchTermsOnly}
                  setShowSearchTermsOnly={setShowSearchTermsOnly}
                />
              ) : (
                <div className="max-w-3xl mx-auto">
                  <FileUpload reportType="searchTerm" fullPage={true} />
                </div>
              )} 
            />
            <Route
              path="/campaign-analysis"
              render={() => hasData ? (
                <CampaignAnalysis 
                  showSearchTermsOnly={showSearchTermsOnly}
                  setShowSearchTermsOnly={setShowSearchTermsOnly}
                />
              ) : (
                <Redirect to="/" />
              )}
            />
            <Route
              path="/search-terms"
              render={() => hasData ? (
                <SearchTermsView />
              ) : (
                <Redirect to="/" />
              )}
            />
            <Route
              path="/opportunities"
              render={() => hasData ? (
                <OpportunitiesPanel />
              ) : (
                <Redirect to="/" />
              )}
            />
            <Route
              path="/wasted-spend"
              render={() => hasData ? (
                <WastedSpendAnalysis />
              ) : (
                <Redirect to="/" />
              )}
            />
          </Switch>
        </div>
      </div>
    </div>
  );
};

export default App;
