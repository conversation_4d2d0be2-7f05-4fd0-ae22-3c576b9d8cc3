import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BaseReport } from '../types/reports';

interface ReportData {
  [key: string]: BaseReport[];
}

interface CampaignState {
  hasData: boolean;
  loading: boolean;
  error: string | null;
  data: ReportData;
  activeReportType: string;
  activeView: string;
  recommendations: any[]; // TODO: Define proper type for recommendations
}

const initialState: CampaignState = {
  hasData: false,
  loading: false,
  error: null,
  data: {},
  activeReportType: 'campaigns',
  activeView: 'dashboard',
  recommendations: []
};

const campaignSlice = createSlice({
  name: 'campaign',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setActiveReportType: (state, action: PayloadAction<string>) => {
      state.activeReportType = action.payload;
    },
    setActiveView: (state, action: PayloadAction<string>) => {
      state.activeView = action.payload;
    },
    processSpreadsheetData: (state, action: PayloadAction<any[]>) => {
      const processedData = action.payload.map(row => {
        // Helper function to safely convert string numbers
        const parseNumber = (value: any): number => {
          if (typeof value === 'number') return value;
          if (!value) return 0;
          const cleanValue = String(value).replace(/[$,%]/g, '');
          return Number(cleanValue) || 0;
        };

        // Calculate ACOS if we have sales and spend
        const spend = parseNumber(row['Spend']);
        const sales = parseNumber(row['7 Day Total Sales ']); // Note the trailing space
        const calculatedAcos = sales > 0 ? (spend / sales) * 100 : 0;

        return {
          startDate: row['Start Date'] || '',
          endDate: row['End Date'] || '',
          portfolioName: row['Portfolio name'] || '',
          currency: row['Currency'] || 'USD',
          campaignName: row['Campaign Name'] || '',
          adGroupName: row['Ad Group Name'] || '',
          targeting: row['Targeting'] || '',
          matchType: row['Match Type'] || '',
          customerSearchTerm: row['Customer Search Term'] || '',
          impressions: parseNumber(row['Impressions']),
          clicks: parseNumber(row['Clicks']),
          ctr: parseNumber(row['Click-Thru Rate (CTR)']),
          cpc: parseNumber(row['Cost Per Click (CPC)']),
          spend: spend,
          sevenDayTotalSales: sales,
          acos: calculatedAcos,
          roas: parseNumber(row['Total Return on Advertising Spend (ROAS)']),
          sevenDayTotalOrders: parseNumber(row['7 Day Total Orders (#)']),
          sevenDayTotalUnits: parseNumber(row['7 Day Total Units (#)']),
          sevenDayConversionRate: parseNumber(row['7 Day Conversion Rate']),
          sevenDayAdvertisedSkuUnits: parseNumber(row['7 Day Advertised SKU Units (#)']),
          sevenDayOtherSkuUnits: parseNumber(row['7 Day Other SKU Units (#)']),
          sevenDayAdvertisedSkuSales: parseNumber(row['7 Day Advertised SKU Sales ']), // Note the trailing space
          sevenDayOtherSkuSales: parseNumber(row['7 Day Other SKU Sales ']) // Note the trailing space
        };
      });
      
      state.data[state.activeReportType] = processedData;
      state.hasData = true;
      state.loading = false;
      state.error = null;
    },
    setRecommendations: (state, action: PayloadAction<any[]>) => {
      state.recommendations = action.payload;
    }
  },
});

export const { 
  setLoading, 
  setError, 
  setActiveReportType, 
  setActiveView,
  processSpreadsheetData,
  setRecommendations 
} = campaignSlice.actions;

export default campaignSlice.reducer;
