import React, { useMemo, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend,
  ResponsiveContainer, LineChart, Line, AreaChart, Area, ComposedChart
} from 'recharts';
import { BaseReport } from '../types/reports';

interface Props {
  campaignName: string;
  data: BaseReport[];
  onClose: () => void;
}

interface KeywordMetrics {
  searchTerm: string;
  impressions: number;
  clicks: number;
  spend: number;
  sales: number;
  orders: number;
  acos: number;
  ctr: number;
  conversionRate: number;
  costPerClick: number;
  costPerOrder: number;
}

const CampaignDetail: React.FC<Props> = ({ campaignName, data, onClose }) => {
  const [sortConfig, setSortConfig] = useState<{ key: keyof KeywordMetrics; direction: 'asc' | 'desc' }>({
    key: 'sales',
    direction: 'desc'
  });
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [searchTerm, setSearchTerm] = useState('');

  const keywordMetrics = useMemo(() => {
    const metrics = new Map<string, KeywordMetrics>();
    
    data.forEach(item => {
      if (item.campaignName !== campaignName || !item.customerSearchTerm) return;

      const existing = metrics.get(item.customerSearchTerm) || {
        searchTerm: item.customerSearchTerm,
        impressions: 0,
        clicks: 0,
        spend: 0,
        sales: 0,
        orders: 0,
        acos: 0,
        ctr: 0,
        conversionRate: 0,
        costPerClick: 0,
        costPerOrder: 0
      };

      existing.impressions += Number(item.impressions) || 0;
      existing.clicks += Number(item.clicks) || 0;
      existing.spend += Number(item.spend) || 0;
      existing.sales += Number(item.sevenDayTotalSales) || 0;
      existing.orders += Number(item.sevenDayTotalOrders) || 0;

      // Calculate derived metrics
      existing.acos = existing.sales > 0 ? (existing.spend / existing.sales) * 100 : 0;
      existing.ctr = existing.impressions > 0 ? (existing.clicks / existing.impressions) * 100 : 0;
      existing.conversionRate = existing.clicks > 0 ? (existing.orders / existing.clicks) * 100 : 0;
      existing.costPerClick = existing.clicks > 0 ? existing.spend / existing.clicks : 0;
      existing.costPerOrder = existing.orders > 0 ? existing.spend / existing.orders : 0;

      metrics.set(item.customerSearchTerm, existing);
    });

    return Array.from(metrics.values());
  }, [data, campaignName]);

  const sortedKeywords = useMemo(() => {
    return [...keywordMetrics]
      .filter(item => item.searchTerm.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        const multiplier = sortConfig.direction === 'asc' ? 1 : -1;
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        return ((aValue as number) - (bValue as number)) * multiplier;
      });
  }, [keywordMetrics, sortConfig, searchTerm]);

  const campaignSummary = useMemo(() => {
    return keywordMetrics.reduce((acc, curr) => ({
      impressions: acc.impressions + curr.impressions,
      clicks: acc.clicks + curr.clicks,
      spend: acc.spend + curr.spend,
      sales: acc.sales + curr.sales,
      orders: acc.orders + curr.orders
    }), {
      impressions: 0,
      clicks: 0,
      spend: 0,
      sales: 0,
      orders: 0
    });
  }, [keywordMetrics]);

  const handleSort = (key: keyof KeywordMetrics) => {
    setSortConfig(current => ({
      key,
      direction: current.key === key && current.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const chartData = useMemo(() => {
    return sortedKeywords.slice(0, 10).map(kw => ({
      name: kw.searchTerm,
      sales: kw.sales,
      spend: kw.spend,
      acos: kw.acos
    }));
  }, [sortedKeywords]);

  const performanceChartData = useMemo(() => {
    return sortedKeywords.slice(0, 10).map(item => ({
      name: item.searchTerm,
      sales: parseFloat(item.sales.toFixed(2)),
      spend: parseFloat(item.spend.toFixed(2)),
      acos: parseFloat(item.acos.toFixed(2))
    }));
  }, [sortedKeywords]);

  const topPerformingChartData = useMemo(() => {
    return [...sortedKeywords]
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 10)
      .map(item => ({
        name: item.searchTerm,
        sales: parseFloat(item.sales.toFixed(2)),
        spend: parseFloat(item.spend.toFixed(2)),
        acos: parseFloat(item.acos.toFixed(2))
      }));
  }, [sortedKeywords]);

  const getPerformanceEmoji = (metric: number, type: 'acos' | 'convRate' | 'ctr') => {
    switch (type) {
      case 'acos':
        if (metric === 0) return '⚪️';
        if (metric < 30) return '🟢';
        if (metric < 50) return '🟡';
        return '🔴';
      case 'convRate':
        if (metric === 0) return '⚪️';
        if (metric > 10) return '🟢';
        if (metric > 5) return '🟡';
        return '🔴';
      case 'ctr':
        if (metric === 0) return '⚪️';
        if (metric > 0.5) return '🟢';
        if (metric > 0.3) return '🟡';
        return '🔴';
      default:
        return '⚪️';
    }
  };

  const formatMetric = (value: number, type: 'currency' | 'percentage' | 'number') => {
    if (value === 0) return 'N/A';
    switch (type) {
      case 'currency':
        return `$${value.toFixed(2)}`;
      case 'percentage':
        return `${value.toFixed(2)}%`;
      default:
        return value.toLocaleString();
    }
  };

  const handleCampaignClick = (campaignName: string) => {
    setSearchTerm(campaignName);
    // Add any additional filtering logic here
  };

  // Pagination
  const pageCount = Math.ceil(sortedKeywords.length / itemsPerPage);
  const paginatedKeywords = sortedKeywords.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Define consistent theme colors
  const theme = {
    sales: '#4F46E5', // Indigo
    spend: '#10B981', // Emerald
    acos: '#F59E0B'   // Amber
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-7xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{campaignName}</h2>
            <p className="text-sm text-gray-500 mt-1">Detailed Performance Analysis</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors duration-150"
          >
            <svg className="h-6 w-6" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          {/* Filter Section */}
          <div className="bg-white rounded-lg shadow p-4 mb-6">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700">Search</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search keywords..."
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700">Sort By</label>
                <select
                  value={sortConfig.key}
                  onChange={(e) => setSortConfig({ key: e.target.value as keyof KeywordMetrics, direction: sortConfig.direction })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="sales">Sales</option>
                  <option value="spend">Spend</option>
                  <option value="acos">ACoS</option>
                  <option value="impressions">Impressions</option>
                  <option value="clicks">Clicks</option>
                </select>
              </div>
              <div className="flex-1 min-w-[200px]">
                <label className="block text-sm font-medium text-gray-700">Order</label>
                <select
                  value={sortConfig.direction}
                  onChange={(e) => setSortConfig({ ...sortConfig, direction: e.target.value as 'asc' | 'desc' })}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="desc">Highest First</option>
                  <option value="asc">Lowest First</option>
                </select>
              </div>
            </div>
          </div>

          {/* KPI Section */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">Spend 💰</div>
              <div className="text-lg font-semibold">${campaignSummary.spend.toFixed(2)}</div>
              <div className="text-xs text-gray-400">{/* spendTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">Sales 💵</div>
              <div className="text-lg font-semibold">${campaignSummary.sales.toFixed(2)}</div>
              <div className="text-xs text-gray-400">{/* salesTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">ACoS 📊</div>
              <div className="text-lg font-semibold">
                {campaignSummary.sales > 0 ? ((campaignSummary.spend / campaignSummary.sales) * 100).toFixed(2) : '0.00'}%
              </div>
              <div className="text-xs text-gray-400">{/* acosTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">ROAS 📈</div>
              <div className="text-lg font-semibold">
                {campaignSummary.spend > 0 ? (campaignSummary.sales / campaignSummary.spend).toFixed(2) : '0.00'}x
              </div>
              <div className="text-xs text-gray-400">{/* roasTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">Impressions 👁️</div>
              <div className="text-lg font-semibold">{campaignSummary.impressions.toLocaleString()}</div>
              <div className="text-xs text-gray-400">{/* impressionsTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">Clicks 🖱️</div>
              <div className="text-lg font-semibold">{campaignSummary.clicks.toLocaleString()}</div>
              <div className="text-xs text-gray-400">{/* clicksTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">CTR 🎯</div>
              <div className="text-lg font-semibold">
                {campaignSummary.impressions > 0 ? ((campaignSummary.clicks / campaignSummary.impressions) * 100).toFixed(2) : '0.00'}%
              </div>
              <div className="text-xs text-gray-400">{/* ctrTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-500">Conv. Rate 🎉</div>
              <div className="text-lg font-semibold">
                {campaignSummary.clicks > 0 ? ((campaignSummary.orders / campaignSummary.clicks) * 100).toFixed(2) : '0.00'}%
              </div>
              <div className="text-xs text-gray-400">{/* convRateTrend === 'up' ? '↑' : '↓' */}</div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Performance Overview Chart */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Keyword Trends</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={performanceChartData} margin={{ top: 20, right: 30, left: 20, bottom: 90 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end" 
                      height={80}
                      interval={0}
                      tick={{ fontSize: 11 }}
                    />
                    <YAxis 
                      yAxisId="left" 
                      orientation="left" 
                      stroke={theme.sales}
                      tickFormatter={(value) => `$${value}`}
                    />
                    <YAxis 
                      yAxisId="right" 
                      orientation="right" 
                      stroke={theme.acos}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Tooltip 
                      formatter={(value: number, name: string) => {
                        if (name === 'ACoS') return [`${value.toFixed(2)}%`, name];
                        return [`$${value.toFixed(2)}`, name];
                      }}
                    />
                    <Legend 
                      verticalAlign="bottom" 
                      height={36}
                      wrapperStyle={{ paddingTop: '20px' }}
                    />
                    <Area 
                      yAxisId="left" 
                      type="monotone" 
                      dataKey="sales" 
                      name="Sales" 
                      stroke={theme.sales}
                      fill={theme.sales}
                      fillOpacity={0.1}
                    />
                    <Area 
                      yAxisId="left" 
                      type="monotone" 
                      dataKey="spend" 
                      name="Spend" 
                      stroke={theme.spend}
                      fill={theme.spend}
                      fillOpacity={0.1}
                    />
                    <Line 
                      yAxisId="right" 
                      type="monotone" 
                      dataKey="acos" 
                      name="ACoS" 
                      stroke={theme.acos}
                      dot={false}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Top Performing Keywords Chart */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Top Keywords by Sales</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={topPerformingChartData} margin={{ top: 20, right: 30, left: 20, bottom: 90 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end" 
                      height={80}
                      interval={0}
                      tick={{ fontSize: 11 }}
                    />
                    <YAxis 
                      yAxisId="left" 
                      orientation="left" 
                      stroke={theme.sales}
                      tickFormatter={(value) => `$${value}`}
                    />
                    <Tooltip 
                      formatter={(value: number) => [`$${value.toFixed(2)}`, 'Sales']}
                    />
                    <Legend 
                      verticalAlign="bottom" 
                      height={36}
                      wrapperStyle={{ paddingTop: '20px' }}
                    />
                    <Bar 
                      yAxisId="left" 
                      dataKey="sales" 
                      name="Sales" 
                      fill={theme.sales}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Campaign Summary */}
          <div className="grid grid-cols-5 gap-4 mb-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg shadow-sm">
              <h3 className="text-sm font-medium text-blue-900">Total Sales 💰</h3>
              <p className="mt-2 text-lg font-semibold text-blue-900">${campaignSummary.sales.toFixed(2)}</p>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg shadow-sm">
              <h3 className="text-sm font-medium text-green-900">Total Orders 📦</h3>
              <p className="mt-2 text-lg font-semibold text-green-900">{campaignSummary.orders}</p>
            </div>
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg shadow-sm">
              <h3 className="text-sm font-medium text-yellow-900">Total Spend 💸</h3>
              <p className="mt-2 text-lg font-semibold text-yellow-900">${campaignSummary.spend.toFixed(2)}</p>
            </div>
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg shadow-sm">
              <h3 className="text-sm font-medium text-purple-900">Total Clicks 🖱️</h3>
              <p className="mt-2 text-lg font-semibold text-purple-900">{campaignSummary.clicks}</p>
            </div>
            <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-4 rounded-lg shadow-sm">
              <h3 className="text-sm font-medium text-pink-900">Impressions 👁️</h3>
              <p className="mt-2 text-lg font-semibold text-pink-900">{campaignSummary.impressions}</p>
            </div>
          </div>

          {/* Keywords Table */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Campaign
                    </th>
                    {Object.keys(paginatedKeywords[0] || {}).map((key) => (
                      <th
                        key={key}
                        onClick={() => handleSort(key as keyof KeywordMetrics)}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      >
                        {key === 'searchTerm' ? 'Search Term 🔍' : key.charAt(0).toUpperCase() + key.slice(1)}
                        {sortConfig.key === key && (
                          <span className="ml-1">
                            {sortConfig.direction === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedKeywords.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <button
                          onClick={() => handleCampaignClick(item.searchTerm)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {item.searchTerm}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.impressions, 'number')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.clicks, 'number')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.spend, 'currency')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.sales, 'currency')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.orders, 'number')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          {getPerformanceEmoji(item.acos, 'acos')}
                          <span className="ml-2">{formatMetric(item.acos, 'percentage')}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          {getPerformanceEmoji(item.conversionRate, 'convRate')}
                          <span className="ml-2">{formatMetric(item.conversionRate, 'percentage')}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center">
                          {getPerformanceEmoji(item.ctr, 'ctr')}
                          <span className="ml-2">{formatMetric(item.ctr, 'percentage')}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.costPerClick, 'currency')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatMetric(item.costPerOrder, 'currency')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between items-center">
                <button
                  onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <span className="text-sm text-gray-700">
                  Page {currentPage} of {pageCount}
                </span>
                <button
                  onClick={() => setCurrentPage(p => Math.min(pageCount, p + 1))}
                  disabled={currentPage === pageCount}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CampaignDetail;
