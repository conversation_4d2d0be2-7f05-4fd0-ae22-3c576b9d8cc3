import React, { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { processSpreadsheetData, setLoading, setError, setActiveView } from '../redux/campaignSlice';
import * as XLSX from 'xlsx';
import { useHistory } from 'react-router-dom';

// Required columns for the spreadsheet
const REQUIRED_COLUMNS = [
  'startDate',
  'endDate',
  'campaignName',
  'impressions',
  'clicks',
  'spend',
  'sevenDayTotalSales',
  'acos'
];

const UploadButton: React.FC = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [processingStatus, setProcessingStatus] = useState<string>('');

  const validateSpreadsheetFormat = (data: any[]): boolean => {
    if (data.length === 0) {
      throw new Error('Spreadsheet is empty');
    }

    const firstRow = data[0];
    const missingColumns = REQUIRED_COLUMNS.filter(col => 
      !Object.keys(firstRow).some(key => 
        key.toLowerCase().replace(/[^a-z0-9]/g, '') === col.toLowerCase()
      )
    );

    if (missingColumns.length > 0) {
      throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
    }

    return true;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    dispatch(setLoading(true));
    setProcessingStatus('');
    
    const reader = new FileReader();
    reader.onerror = () => {
      dispatch(setError('Error reading file'));
      dispatch(setLoading(false));
      setProcessingStatus('Error reading file');
    };
    
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(sheet);
        
        // Validate spreadsheet format
        validateSpreadsheetFormat(jsonData);
        
        setProcessingStatus(`Successfully processed ${jsonData.length} rows`);
        dispatch(processSpreadsheetData(jsonData));
        
        // Clear the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        // Navigate to dashboard after successful upload
        setTimeout(() => {
          dispatch(setActiveView('dashboard')); // Update the active view
          history.push('/');
        }, 1500);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Error processing spreadsheet';
        dispatch(setError(errorMessage));
        dispatch(setLoading(false));
        setProcessingStatus(`Error: ${errorMessage}`);
      }
    };
    reader.readAsBinaryString(file);
  };

  return (
    <div className="relative">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileUpload}
        accept=".xlsx,.xls,.csv"
        style={{ display: 'none' }}
      />
      <button 
        onClick={() => fileInputRef.current?.click()}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Upload Spreadsheet
      </button>
      {processingStatus && (
        <div className="absolute top-full left-0 mt-2 text-sm text-gray-700">
          {processingStatus}
        </div>
      )}
    </div>
  );
};

export default UploadButton;
