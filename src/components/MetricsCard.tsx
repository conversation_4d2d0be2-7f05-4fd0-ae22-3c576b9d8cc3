import React from 'react';

interface MetricsCardProps {
  title: string;
  value: number;
  format: 'percentage' | 'decimal' | 'currency';
  trend?: number;
}

const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  format,
  trend
}) => {
  const formatValue = (val: number): string => {
    switch (format) {
      case 'percentage':
        return `${val.toFixed(2)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(val);
      case 'decimal':
        return val.toFixed(2);
      default:
        return val.toString();
    }
  };

  const getTrendColor = (trendValue: number): string => {
    if (trendValue > 0) return 'text-green-500';
    if (trendValue < 0) return 'text-red-500';
    return 'text-gray-500';
  };

  const getTrendIcon = (trendValue: number): string => {
    if (trendValue > 0) return '↑';
    if (trendValue < 0) return '↓';
    return '→';
  };

  return (
    <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        {trend !== undefined && (
          <div className={`flex items-center ${getTrendColor(trend)} text-sm font-medium`}>
            <span className="mr-1">{getTrendIcon(trend)}</span>
            {Math.abs(trend)}%
          </div>
        )}
      </div>
      <div className="mt-2">
        <div className="text-3xl font-bold text-gray-900">
          {formatValue(value)}
        </div>
      </div>
      {trend !== undefined && (
        <div className="mt-4 flex items-center text-xs">
          <div className={`flex items-center ${getTrendColor(trend)}`}>
            <span className="mr-1">
              {trend > 0 ? 'Increased' : trend < 0 ? 'Decreased' : 'No change'}
            </span>
            <span>vs. previous period</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MetricsCard;
