import React, { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { RootState } from '../store/store';
import {
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend,
  ResponsiveContainer, Pie<PERSON>hart, Pie, Cell
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

interface CampaignFilters {
  searchTerm: string;
  minSpend: number;
  minSales: number;
  minAcos: number;
  minRoas: number;
  minOrders: number;
  minCtr: number;
  minConversionRate: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  performanceFilter: 'all' | 'high-performing' | 'low-performing';
}

const formatMetric = (value: number | undefined, type: 'currency' | 'percentage' | 'number'): string => {
  if (value === undefined || value === null || isNaN(value) || !isFinite(value)) return 'N/A';
  if (value === 0) return '0';

  switch (type) {
    case 'currency':
      return `$${value.toFixed(2)}`;
    case 'percentage':
      return `${value.toFixed(2)}%`;
    default:
      return value.toLocaleString();
  }
};

const getPerformanceIndicator = (metric: number, type: 'acos' | 'roas' | 'ctr' | 'cvr') => {
  if (!isFinite(metric) || metric === 0) return '⚪️';
  
  switch (type) {
    case 'acos':
      if (metric < 30) return '🟢';
      if (metric < 50) return '🟡';
      return '🔴';
    case 'roas':
      if (metric > 3) return '🟢';
      if (metric > 1.5) return '🟡';
      return '🔴';
    case 'ctr':
      if (metric > 0.5) return '🟢';
      if (metric > 0.3) return '🟡';
      return '🔴';
    case 'cvr':
      if (metric > 10) return '🟢';
      if (metric > 5) return '🟡';
      return '🔴';
    default:
      return '⚪️';
  }
};

const CampaignsView: React.FC = () => {
  const { data, activeReportType } = useSelector((state: RootState) => state.campaign);
  const history = useHistory();
  const [filters, setFilters] = useState<CampaignFilters>({
    searchTerm: '',
    minSpend: 0,
    minSales: 0,
    minAcos: 0,
    minRoas: 0,
    minOrders: 0,
    minCtr: 0,
    minConversionRate: 0,
    sortBy: 'spend',
    sortOrder: 'desc',
    performanceFilter: 'all'
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCampaign, setSelectedCampaign] = useState<string | null>(null);
  const itemsPerPage = 25;

  const campaigns = useMemo(() => {
    const campaignMap = new Map();
    
    data[activeReportType]?.forEach((item: any) => {
      if (!item.campaignName) return;

      const existing = campaignMap.get(item.campaignName) || {
        name: item.campaignName,
        spend: 0,
        sales: 0,
        impressions: 0,
        clicks: 0,
        orders: 0,
        acos: 0,
        roas: 0,
        ctr: 0,
        conversionRate: 0,
        costPerClick: 0,
        newToBrand: item.targeting?.toLowerCase().includes('new-to-brand')
      };

      existing.spend += Number(item.spend) || 0;
      existing.sales += Number(item.sevenDayTotalSales) || 0;
      existing.impressions += Number(item.impressions) || 0;
      existing.clicks += Number(item.clicks) || 0;
      existing.orders += Number(item.sevenDayTotalOrders) || 0;

      // Calculate metrics
      existing.acos = existing.sales > 0 ? (existing.spend / existing.sales) * 100 : 0;
      existing.roas = existing.spend > 0 ? existing.sales / existing.spend : 0;
      existing.ctr = existing.impressions > 0 ? (existing.clicks / existing.impressions) * 100 : 0;
      existing.conversionRate = existing.clicks > 0 ? (existing.orders / existing.clicks) * 100 : 0;
      existing.costPerClick = existing.clicks > 0 ? existing.spend / existing.clicks : 0;

      campaignMap.set(item.campaignName, existing);
    });

    return Array.from(campaignMap.values());
  }, [data, activeReportType]);

  const filteredCampaigns = useMemo(() => {
    return campaigns.filter(camp => {
      const matchesSearch = camp.name.toLowerCase().includes(filters.searchTerm.toLowerCase());
      const meetsSpend = camp.spend >= filters.minSpend;
      const meetsSales = camp.sales >= filters.minSales;
      const meetsAcos = camp.acos >= filters.minAcos;
      const meetsRoas = camp.roas >= filters.minRoas;
      const meetsOrders = camp.orders >= filters.minOrders;
      const meetsCtr = camp.ctr >= filters.minCtr;
      const meetsConversionRate = camp.conversionRate >= filters.minConversionRate;
      
      let meetsPerformance = true;
      if (filters.performanceFilter === 'high-performing') {
        meetsPerformance = camp.acos < 30 || camp.roas > 3;
      } else if (filters.performanceFilter === 'low-performing') {
        meetsPerformance = camp.acos > 50 || camp.roas < 1.5;
      }

      return matchesSearch && meetsSpend && meetsSales && meetsAcos && meetsRoas && 
             meetsOrders && meetsCtr && meetsConversionRate && meetsPerformance;
    }).sort((a, b) => {
      const multiplier = filters.sortOrder === 'asc' ? 1 : -1;
      const aValue = Number(a[filters.sortBy as keyof typeof a]) || 0;
      const bValue = Number(b[filters.sortBy as keyof typeof b]) || 0;
      return (aValue - bValue) * multiplier;
    });
  }, [campaigns, filters]);

  const paginatedCampaigns = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredCampaigns.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredCampaigns, currentPage]);

  const totalPages = Math.ceil(filteredCampaigns.length / itemsPerPage);

  const campaignMetrics = useMemo(() => {
    return filteredCampaigns.reduce((acc, camp) => ({
      totalSpend: acc.totalSpend + camp.spend,
      totalSales: acc.totalSales + camp.sales,
      totalImpressions: acc.totalImpressions + camp.impressions,
      totalClicks: acc.totalClicks + camp.clicks,
      totalOrders: acc.totalOrders + camp.orders
    }), {
      totalSpend: 0,
      totalSales: 0,
      totalImpressions: 0,
      totalClicks: 0,
      totalOrders: 0
    });
  }, [filteredCampaigns]);

  const chartData = useMemo(() => {
    return filteredCampaigns.slice(0, 10).map(camp => ({
      name: camp.name,
      spend: camp.spend,
      sales: camp.sales,
      acos: camp.acos
    }));
  }, [filteredCampaigns]);

  const handleCampaignClick = (campaignName: string) => {
    history.push('/search-terms', { selectedCampaign: campaignName });
  };

  const getCampaignDetails = (campaignName: string) => {
    const campaignData = data[activeReportType]?.filter((item: any) => item.campaignName === campaignName) || [];
    return {
      searchTerms: campaignData.map((item: any) => ({
        term: item.customerSearchTerm,
        spend: item.spend,
        sales: item.sales,
        impressions: item.impressions,
        clicks: item.clicks,
        orders: item.orders,
        acos: item.acos,
        roas: item.roas,
        ctr: item.ctr,
        conversionRate: item.conversionRate,
        costPerClick: item.costPerClick
      }))
    };
  };

  return (
    <div className="p-6 space-y-6">
      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
        <h2 className="text-xl font-medium text-gray-900 mb-4">Campaign Filters</h2>
        
        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Search Campaign</label>
            <input
              type="text"
              value={filters.searchTerm}
              onChange={e => setFilters(f => ({ ...f, searchTerm: e.target.value }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Filter campaigns..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Performance</label>
            <select
              value={filters.performanceFilter}
              onChange={e => setFilters(f => ({ ...f, performanceFilter: e.target.value as any }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            >
              <option value="all">All Campaigns</option>
              <option value="high-performing">High Performing</option>
              <option value="low-performing">Low Performing</option>
            </select>
          </div>
        </div>

        {/* Metric Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. Spend ($)</label>
            <input
              type="number"
              value={filters.minSpend}
              onChange={e => setFilters(f => ({ ...f, minSpend: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. Sales ($)</label>
            <input
              type="number"
              value={filters.minSales}
              onChange={e => setFilters(f => ({ ...f, minSales: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. ACoS (%)</label>
            <input
              type="number"
              value={filters.minAcos}
              onChange={e => setFilters(f => ({ ...f, minAcos: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. ROAS</label>
            <input
              type="number"
              value={filters.minRoas}
              onChange={e => setFilters(f => ({ ...f, minRoas: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. Orders</label>
            <input
              type="number"
              value={filters.minOrders}
              onChange={e => setFilters(f => ({ ...f, minOrders: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. CTR (%)</label>
            <input
              type="number"
              value={filters.minCtr}
              onChange={e => setFilters(f => ({ ...f, minCtr: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Min. Conv. Rate (%)</label>
            <input
              type="number"
              value={filters.minConversionRate}
              onChange={e => setFilters(f => ({ ...f, minConversionRate: Number(e.target.value) }))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* KPI Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-900">Total Spend 💰</h3>
              <p className="mt-1 text-2xl font-bold text-blue-900">
                {formatMetric(campaignMetrics.totalSpend, 'currency')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-green-900">Total Sales 💸</h3>
              <p className="mt-1 text-2xl font-bold text-green-900">
                {formatMetric(campaignMetrics.totalSales, 'currency')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-purple-900">Average ACoS 📊</h3>
              <p className="mt-1 text-2xl font-bold text-purple-900">
                {formatMetric(campaignMetrics.totalSpend / campaignMetrics.totalSales * 100, 'percentage')} 
                {getPerformanceIndicator(campaignMetrics.totalSpend / campaignMetrics.totalSales * 100, 'acos')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-yellow-900">Average ROAS 📈</h3>
              <p className="mt-1 text-2xl font-bold text-yellow-900">
                {formatMetric(campaignMetrics.totalSales / campaignMetrics.totalSpend, 'number')} 
                {getPerformanceIndicator(campaignMetrics.totalSales / campaignMetrics.totalSpend, 'roas')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-red-50 to-red-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-red-900">Total Orders 📦</h3>
              <p className="mt-1 text-2xl font-bold text-red-900">
                {formatMetric(campaignMetrics.totalOrders, 'number')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-indigo-900">Average CTR 🎯</h3>
              <p className="mt-1 text-2xl font-bold text-indigo-900">
                {formatMetric(campaignMetrics.totalClicks / campaignMetrics.totalImpressions * 100, 'percentage')} 
                {getPerformanceIndicator(campaignMetrics.totalClicks / campaignMetrics.totalImpressions * 100, 'ctr')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-pink-50 to-pink-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-pink-900">Average CVR 🎯</h3>
              <p className="mt-1 text-2xl font-bold text-pink-900">
                {formatMetric(campaignMetrics.totalOrders / campaignMetrics.totalClicks * 100, 'percentage')} 
                {getPerformanceIndicator(campaignMetrics.totalOrders / campaignMetrics.totalClicks * 100, 'cvr')}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-cyan-50 to-cyan-100 p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-cyan-900">Average CPC 💵</h3>
              <p className="mt-1 text-2xl font-bold text-cyan-900">
                {formatMetric(campaignMetrics.totalSpend / campaignMetrics.totalClicks, 'currency')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Spend vs Sales Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Spend vs Sales by Campaign</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={paginatedCampaigns}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `$${value.toFixed(2)}`} />
                <Tooltip 
                  formatter={(value: any) => [`$${Number(value).toFixed(2)}`, '']}
                  labelStyle={{ color: '#374151' }}
                  contentStyle={{ backgroundColor: '#ffffff', borderColor: '#e5e7eb' }}
                />
                <Legend />
                <Bar dataKey="spend" name="Spend" fill="#0088FE" />
                <Bar dataKey="sales" name="Sales" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Performance Metrics Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Campaign Performance Metrics</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={paginatedCampaigns}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis yAxisId="left" tickFormatter={(value) => `${value.toFixed(2)}%`} />
                <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => value.toFixed(2)} />
                <Tooltip 
                  formatter={(value: any, name: string) => {
                    if (name === 'ROAS') return [value.toFixed(2), name];
                    return [`${value.toFixed(2)}%`, name];
                  }}
                  labelStyle={{ color: '#374151' }}
                  contentStyle={{ backgroundColor: '#ffffff', borderColor: '#e5e7eb' }}
                />
                <Legend />
                <Bar yAxisId="left" dataKey="acos" name="ACoS" fill="#8884d8" />
                <Bar yAxisId="left" dataKey="ctr" name="CTR" fill="#82ca9d" />
                <Bar yAxisId="right" dataKey="roas" name="ROAS" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Campaigns Table */}
      <div className="mt-8">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'name', sortOrder: f.sortBy === 'name' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  Campaign Name {filters.sortBy === 'name' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'spend', sortOrder: f.sortBy === 'spend' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  Spend {filters.sortBy === 'spend' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'sales', sortOrder: f.sortBy === 'sales' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  Sales {filters.sortBy === 'sales' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'acos', sortOrder: f.sortBy === 'acos' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  ACoS {filters.sortBy === 'acos' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'roas', sortOrder: f.sortBy === 'roas' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  ROAS {filters.sortBy === 'roas' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'orders', sortOrder: f.sortBy === 'orders' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  Orders {filters.sortBy === 'orders' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'ctr', sortOrder: f.sortBy === 'ctr' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  CTR {filters.sortBy === 'ctr' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => setFilters(f => ({ ...f, sortBy: 'conversionRate', sortOrder: f.sortBy === 'conversionRate' ? (f.sortOrder === 'asc' ? 'desc' : 'asc') : 'asc' }))}>
                  Conv. Rate {filters.sortBy === 'conversionRate' && (filters.sortOrder === 'asc' ? '↑' : '↓')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedCampaigns.map((campaign: any) => (
                <React.Fragment key={campaign.name}>
                  <tr 
                    className={`hover:bg-gray-50 cursor-pointer ${selectedCampaign === campaign.name ? 'bg-blue-50' : ''}`}
                    onClick={() => handleCampaignClick(campaign.name)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{campaign.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${campaign.spend.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${campaign.sales.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPerformanceIndicator(campaign.acos, 'acos')} {campaign.acos.toFixed(2)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPerformanceIndicator(campaign.roas, 'roas')} {campaign.roas.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{campaign.orders}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPerformanceIndicator(campaign.ctr, 'ctr')} {campaign.ctr.toFixed(2)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPerformanceIndicator(campaign.conversionRate, 'cvr')} {campaign.conversionRate.toFixed(2)}%
                    </td>
                  </tr>
                  {selectedCampaign === campaign.name && (
                    <tr>
                      <td colSpan={12}>
                        <div className="p-4 bg-gray-50">
                          <h4 className="font-medium text-gray-900 mb-2">Search Terms for {campaign.name}</h4>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-white">
                                <tr>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Search Term</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spend</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ACoS</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ROAS</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                                </tr>
                              </thead>
                              <tbody>
                                {getCampaignDetails(campaign.name).searchTerms.map((term: any, idx: number) => (
                                  <tr key={idx} className="hover:bg-gray-50">
                                    <td className="px-3 py-2 text-sm text-gray-900">{term.term}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatMetric(term.spend, 'currency')}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatMetric(term.sales, 'currency')}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatMetric(term.acos, 'percentage')} {getPerformanceIndicator(term.acos, 'acos')}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatMetric(term.roas, 'number')} {getPerformanceIndicator(term.roas, 'roas')}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{formatMetric(term.orders, 'number')}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
        <div className="flex-1 flex justify-between items-center">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
              <span className="font-medium">
                {Math.min(currentPage * itemsPerPage, filteredCampaigns.length)}
              </span>{' '}
              of <span className="font-medium">{filteredCampaigns.length}</span> campaigns
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CampaignsView;
