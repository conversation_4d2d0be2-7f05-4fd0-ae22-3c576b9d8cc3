// At the top of the file, update imports
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Chart } from 'react-chartjs-2';
import { RootState } from '../store/store';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface Props {
  showSearchTermsOnly?: boolean;
  setShowSearchTermsOnly?: (value: boolean) => void;
}

interface CampaignMetrics {
  name: string;
  spend: number;
  sales: number;
  acos: number;
  roas: number;
  clicks: number;
  impressions: number;
  ctr: number;
  cpc: number;
  orders: number;
  cvr: number;
  startDate: string;
  endDate: string;
  campaignName: string;
}

interface DateRange {
  start: string;
  end: string;
}

interface FilterRange {
  min: number;
  max: number;
}

interface Filters {
  spend: FilterRange;
  sales: FilterRange;
  acos: FilterRange;
  roas: FilterRange;
  clicks: FilterRange;
  impressions: FilterRange;
  ctr: FilterRange;
  cpc: FilterRange;
  orders: FilterRange;
  cvr: FilterRange;
  campaignName: string;
}

type MetricType = 'campaignName' | 'spend' | 'sales' | 'acos' | 'roas' | 'orders' | 'cvr' | 'ctr' | 'cvr' | 'impressions' | 'clicks' | 'orders' | 'cpc' | 'name';
type TimeRangeType = 'all' | '30d' | '7d';

interface SearchTermFilters {
  searchTerm: string;
  minImpressions: string;
  minClicks: string;
  minCtr: string;
  minSpend: string;
  minSales: string;
  minOrders: string;
  minConversionRate: string;
  maxAcos: string;
  minRoas: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  campaignName: string;
  totalOrders: string;
}

interface SearchTermMetrics {
  customerSearchTerm: string;
  campaignName: string;
  spend: number;
  sales: number;
  acos: number;
  roas: number;
  clicks: number;
  impressions: number;
  ctr: number;
  cpc: number;
  orders: number;
  cvr: number;
}

interface ActiveFilter {
  key: keyof SearchTermFilters;
  label: string;
  value: string;
}

interface LocationState {
  campaignName?: string;
  filters?: {
    campaignName: string;
  };
  activeTab?: string;
}

const initialFilters: SearchTermFilters = {
  searchTerm: '',
  minImpressions: '',
  minClicks: '',
  minCtr: '',
  minSpend: '',
  minSales: '',
  minOrders: '',
  minConversionRate: '',
  maxAcos: '',
  minRoas: '',
  sortBy: 'spend',
  sortOrder: 'desc',
  campaignName: '',
  totalOrders: ''
};

interface FilterItem {
  placeholder: string;
  value: keyof SearchTermFilters;
}

const filterInputs: FilterItem[] = [
  { placeholder: "Min Impressions", value: "minImpressions" },
  { placeholder: "Min Clicks", value: "minClicks" },
  { placeholder: "Min CTR %", value: "minCtr" },
  { placeholder: "Min Spend $", value: "minSpend" },
  { placeholder: "Min Sales $", value: "minSales" },
  { placeholder: "Min Orders", value: "minOrders" },
  { placeholder: "Min CVR %", value: "minConversionRate" },
  { placeholder: "Max ACoS %", value: "maxAcos" },
  { placeholder: "Min ROAS", value: "minRoas" },
  { placeholder: "Total Orders", value: "totalOrders" }
];

const calculateMetrics = (item: any): Partial<CampaignMetrics> => {
  // Convert to numbers and handle nulls consistently
  const sales = Number(item.sevenDayTotalSales) || 0;
  const spend = Number(item.spend) || 0;
  const clicks = Number(item.clicks) || 0;
  const impressions = Number(item.impressions) || 0;
  const orders = Number(item.sevenDayTotalOrders) || 0;

  // Calculate derived metrics
  const acos = sales > 0 ? (spend / sales) * 100 : 0;
  const roas = spend > 0 ? sales / spend : 0;
  const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
  const cvr = clicks > 0 ? (orders / clicks) * 100 : 0;
  const cpc = clicks > 0 ? spend / clicks : 0;

  return {
    name: item.campaignName || '',
    spend,
    sales,
    acos,
    roas,
    orders,
    clicks,
    impressions,
    ctr,
    cvr,
    cpc
  };
};

const getTop10Campaigns = (data: any[]): any[] => {
  return data
    .map(item => ({
      name: item.campaignName || '',
      spend: Number(item.spend) || 0,
      sales: Number(item.sales) || 0,
      acos: Number(item.acos) || 0
    }))
    .sort((a, b) => b.spend - a.spend)
    .slice(0, 10);
};

const getTop10PerformingCampaigns = (data: any[]): any[] => {
  return data
    .map(item => ({
      name: item.campaignName || '',
      spend: Number(item.spend) || 0,
      sales: Number(item.sales) || 0,
      acos: Number(item.acos) || 0
    }))
    .sort((a, b) => a.acos - b.acos)
    .slice(0, 10);
};

const getTop10CampaignsBySpend = (data: any[]): any[] => {
  return data
    .map(item => ({
      name: item.campaignName || '',
      spend: Number(item.spend) || 0,
      sales: Number(item.sales) || 0,
      acos: Number(item.acos) || 0
    }))
    .sort((a, b) => b.spend - a.spend)
    .slice(0, 10);
};

const CampaignAnalysis: React.FC<Props> = ({
  showSearchTermsOnly = false,
  setShowSearchTermsOnly = () => {}
}) => {
  const dispatch = useSelector((state: RootState) => state.campaign);
  const { data, activeReportType } = dispatch;
  const reportData = data[activeReportType] || [];

  const [selectedMetric, setSelectedMetric] = useState<string>('spend');
  const [searchTermFilters, setSearchTermFilters] = useState<SearchTermFilters>(initialFilters);
  const [dateRange, setDateRange] = useState<DateRange>({
    start: '',
    end: ''
  });

  const [timeRange, setTimeRange] = useState<TimeRangeType>('all');
  const [sortBy, setSortBy] = useState<MetricType>('sales');
  const [emojiFilter, setEmojiFilter] = useState<'all' | '🟢' | '🟡' | '🔴'>('all');
  
  const [filters, setFilters] = useState<Filters>({
    spend: { min: 0, max: Infinity },
    sales: { min: 0, max: Infinity },
    acos: { min: 0, max: Infinity },
    roas: { min: 0, max: Infinity },
    clicks: { min: 0, max: Infinity },
    impressions: { min: 0, max: Infinity },
    ctr: { min: 0, max: Infinity },
    cpc: { min: 0, max: Infinity },
    orders: { min: 0, max: Infinity },
    cvr: { min: 0, max: Infinity },
    campaignName: ''
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<MetricType>('sales');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const itemsPerPage = 25;

  const history = useHistory();
  const location = useLocation<LocationState>();
  const { campaignName, filters: locationFilters } = location.state || {};

  const isWithinRange = (value: number, range: FilterRange) => {
    return value >= range.min && value <= range.max;
  };

  const isWithinDateRange = (startDate: string, endDate: string) => {
    if (!dateRange.start || !dateRange.end) return true;
    return (
      new Date(startDate) >= new Date(dateRange.start) &&
      new Date(endDate) <= new Date(dateRange.end)
    );
  };

  const handleSearchTermFilterChange = (field: keyof SearchTermFilters, value: string) => {
    setSearchTermFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1);
  };

  const getMetricEmoji = (value: number, metric: string): string => {
    switch (metric) {
      case 'acos':
        return value < 30 ? '🟢' : value < 50 ? '🟡' : '🔴';
      case 'roas':
        return value > 3 ? '🟢' : value > 2 ? '🟡' : '🔴';
      case 'cvr':
        return value > 15 ? '🟢' : value > 10 ? '🟡' : '🔴';
      default:
        return '';
    }
  };

  const formatNumber = (value: number | undefined, type: 'number' | 'currency' | 'decimal' | 'percentage'): string => {
    if (value === undefined || isNaN(value)) return '-';
    
    switch (type) {
      case 'number':
        return Math.round(value).toLocaleString();
      case 'currency':
        return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      case 'decimal':
        return value.toFixed(2);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toString();
    }
  };

  const filteredData = useMemo(() => {
    return data[activeReportType]?.map((item: any) => {
      const sales = Number(item.sevenDayTotalSales) || 0;
      const spend = Number(item.spend) || 0;
      const clicks = Number(item.clicks) || 0;
      const impressions = Number(item.impressions) || 0;
      const orders = Number(item.sevenDayTotalOrders) || 0;

      // Calculate derived metrics
      const acos = sales > 0 ? (spend / sales) * 100 : 0;
      const roas = spend > 0 ? sales / spend : 0;
      const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
      const cvr = clicks > 0 ? (orders / clicks) * 100 : 0;

      return {
        ...item,
        sales,
        spend,
        orders,
        clicks,
        impressions,
        ctr,
        acos,
        roas,
        cvr
      };
    }).filter((item: any) => {
      const campaignNameMatch = !searchTermFilters.campaignName || 
        item.campaignName.toLowerCase().includes(searchTermFilters.campaignName.toLowerCase());
      
      const meetsMinimums = (
        (!searchTermFilters.minSpend || item.spend >= parseFloat(searchTermFilters.minSpend)) &&
        (!searchTermFilters.minSales || item.sales >= parseFloat(searchTermFilters.minSales)) &&
        (!searchTermFilters.minOrders || item.orders >= parseFloat(searchTermFilters.minOrders)) &&
        (!searchTermFilters.maxAcos || item.acos <= parseFloat(searchTermFilters.maxAcos)) &&
        (!searchTermFilters.minRoas || item.roas >= parseFloat(searchTermFilters.minRoas)) &&
        (!searchTermFilters.minConversionRate || item.cvr >= parseFloat(searchTermFilters.minConversionRate)) &&
        (!searchTermFilters.minImpressions || item.impressions >= parseFloat(searchTermFilters.minImpressions)) &&
        (!searchTermFilters.minClicks || item.clicks >= parseFloat(searchTermFilters.minClicks)) &&
        (!searchTermFilters.minCtr || item.ctr >= parseFloat(searchTermFilters.minCtr)) &&
        (!searchTermFilters.totalOrders || item.orders === parseFloat(searchTermFilters.totalOrders))
      );

      return campaignNameMatch && meetsMinimums;
    }) || [];
  }, [data, activeReportType, searchTermFilters]);

  const overallMetrics = useMemo(() => {
    const totals = filteredData.reduce((acc, campaign) => ({
      spend: acc.spend + (Number(campaign.spend) || 0),
      sales: acc.sales + (Number(campaign.sales) || 0),
      orders: acc.orders + (Number(campaign.orders) || 0),
      impressions: acc.impressions + (Number(campaign.impressions) || 0),
      clicks: acc.clicks + (Number(campaign.clicks) || 0)
    }), {
      spend: 0,
      sales: 0,
      orders: 0,
      impressions: 0,
      clicks: 0
    });

    const acos = totals.sales > 0 ? (totals.spend / totals.sales) * 100 : 0;
    const roas = totals.spend > 0 ? totals.sales / totals.spend : 0;
    const ctr = totals.impressions > 0 ? (totals.clicks / totals.impressions) * 100 : 0;
    const cvr = totals.clicks > 0 ? (totals.orders / totals.clicks) * 100 : 0;

    return {
      ...totals,
      acos,
      roas,
      ctr,
      cvr
    };
  }, [filteredData]);

  const performanceChartData = useMemo(() => {
    const top10 = [...filteredData]
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 10);
    
    return {
      labels: top10.map(term => term.customerSearchTerm),
      datasets: [
        {
          type: 'bar' as const,
          label: 'Sales',
          data: top10.map(term => term.sales),
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          borderColor: 'rgb(53, 162, 235)',
          borderWidth: 1,
          yAxisID: 'y'
        },
        {
          type: 'bar' as const,
          label: 'Spend',
          data: top10.map(term => term.spend),
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 1,
          yAxisID: 'y'
        }
      ]
    };
  }, [filteredData]);

  const scatterChartData = useMemo(() => {
    return {
      datasets: [{
        label: 'Search Term Performance',
        data: filteredData.map(term => ({
          x: term.acos,
          y: term.sales,
          r: Math.sqrt(term.spend) / 2 // Size based on spend
        })),
        backgroundColor: filteredData.map(term => {
          if (term.acos < 30) return 'rgba(75, 192, 192, 0.6)';
          if (term.acos < 50) return 'rgba(255, 206, 86, 0.6)';
          return 'rgba(255, 99, 132, 0.6)';
        })
      }]
    };
  }, [filteredData]);

  const performanceChartOptions = useMemo<ChartOptions<'bar'>>(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Top 10 Search Terms by Sales'
      },
      tooltip: {
        callbacks: {
          title(tooltipItems) {
            const index = tooltipItems[0].dataIndex;
            const term = filteredData[index];
            return term?.customerSearchTerm || '';
          },
          label(context) {
            const value = context.parsed.y;
            return `${context.dataset.label}: ${formatNumber(value, 'currency')}`;
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Amount ($)'
        },
        ticks: {
          callback(value) {
            return `$${Number(value).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
          }
        }
      }
    }
  }), [filteredData]);

  const scatterChartOptions = useMemo<ChartOptions<'bubble'>>(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Search Terms ACoS vs Sales (bubble size = spend)'
      },
      tooltip: {
        callbacks: {
          label(context) {
            const term = filteredData[context.dataIndex];
            return [
              `Search Term: ${term.customerSearchTerm}`,
              `ACoS: ${formatNumber(term.acos, 'decimal')}%`,
              `Sales: ${formatNumber(term.sales, 'currency')}`,
              `Spend: ${formatNumber(term.spend, 'currency')}`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'ACoS %'
        },
        ticks: {
          callback(value) {
            return `${Number(value).toFixed(1)}%`;
          }
        }
      },
      y: {
        title: {
          display: true,
          text: 'Sales ($)'
        },
        ticks: {
          callback(value) {
            return `$${Number(value).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
          }
        }
      }
    }
  }), [filteredData]);

  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      const multiplier = sortDirection === 'asc' ? 1 : -1;
      
      if (sortField === 'campaignName') {
        return multiplier * a.campaignName.localeCompare(b.campaignName);
      }
      
      const aValue = Number(a[sortField]) || 0;
      const bValue = Number(b[sortField]) || 0;
      return multiplier * (aValue - bValue);
    });
  }, [filteredData, sortField, sortDirection]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  const getAcosEmoji = (acos: number): string => {
    if (acos < 20) return '🟢'; // Great
    if (acos < 35) return '🟡'; // Good
    return '🔴'; // Needs improvement
  };

  const getRoasEmoji = (roas: number): string => {
    if (roas > 5) return '🟢'; // Great
    if (roas > 3) return '🟡'; // Good
    return '🔴'; // Needs improvement
  };

  const getCtrEmoji = (ctr: number): string => {
    if (ctr >= 1) return '🟢'; // Great
    if (ctr >= 0.5) return '🟡'; // Good
    return '🔴'; // Needs improvement
  };

  const getCvrEmoji = (cvr: number): string => {
    if (cvr > 15) return '🟢'; // Great
    if (cvr > 10) return '🟡'; // Good
    return '🔴'; // Needs improvement
  };

  const handleFilterChange = (metric: keyof Filters, value: FilterRange) => {
    setFilters(prev => ({
      ...prev,
      [metric]: value
    }));
  };

  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };

  const clearFilters = () => {
    setSearchTermFilters(initialFilters);
  };

  const clearFilter = (key: keyof SearchTermFilters) => {
    setSearchTermFilters(prev => ({
      ...prev,
      [key]: ''
    }));
    setCurrentPage(1);
  };

  const handleCampaignClick = (campaignName: string) => {
    history.push('/search-terms', { 
      campaignName,
      filters: {
        ...filters,
        campaignName: campaignName
      }
    });
  };

  useEffect(() => {
    if (locationFilters?.campaignName || campaignName) {
      const newCampaignName = locationFilters?.campaignName || campaignName || '';
      setSearchTermFilters(prev => ({
        ...prev,
        campaignName: newCampaignName
      }));
    }
  }, [locationFilters, campaignName]);

  const getActiveFilters = (): ActiveFilter[] => {
    return Object.entries(searchTermFilters)
      .filter(([key, value]) => {
        if (key === 'campaignName') {
          return value && (value === locationFilters?.campaignName || value === campaignName);
        }
        return value && key !== 'searchTerm' && !['sortBy', 'sortOrder'].includes(key);
      })
      .map(([key, value]) => ({
        key: key as keyof SearchTermFilters,
        label: filterInputs.find(f => f.value === key)?.placeholder || key,
        value: value.toString()
      }));
  };

  const handleSort = (field: MetricType) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleCampaignInputChange = (value: string) => {
    setSearchTermFilters(prev => ({ ...prev, campaignName: value }));
  };

  const handleSearchTermChange = (value: string) => {
    setSearchTermFilters(prev => ({ ...prev, searchTerm: value }));
  };

  const clearAllFilters = () => {
    setSearchTermFilters(initialFilters);
  };

  const clearCampaignFilter = () => {
    setSearchTermFilters(prev => ({
      ...prev,
      campaignName: ''
    }));
    history.replace({ ...location, state: {} });
  };

  const handleRemoveFilter = (key: keyof SearchTermFilters) => {
    if (key === 'campaignName') {
      clearCampaignFilter();
    } else {
      setSearchTermFilters(prev => ({
        ...prev,
        [key]: ''
      }));
    }
  };

  // Add new state for selected KPIs
  const [selectedKPIs, setSelectedKPIs] = useState([
    'impressions',
    'clicks',
    'ctr',
    'spend',
    'sales',
    'acos',
    'roas',
    'orders',
    'cvr'
  ]);

  const availableKPIs = [
    { id: 'impressions', label: 'Impressions', icon: '👁️' },
    { id: 'clicks', label: 'Clicks', icon: '🖱️' },
    { id: 'ctr', label: 'CTR', icon: '📊' },
    { id: 'spend', label: 'Spend', icon: '💰' },
    { id: 'sales', label: 'Sales', icon: '💵' },
    { id: 'acos', label: 'ACoS', icon: '📉' },
    { id: 'roas', label: 'ROAS', icon: '📈' },
    { id: 'orders', label: 'Orders', icon: '📦' },
    { id: 'cvr', label: 'CVR', icon: '🎯' }
  ];

  const handleKPIToggle = (kpiId: string) => {
    if (selectedKPIs.includes(kpiId)) {
      if (selectedKPIs.length > 4) { // Ensure at least 4 KPIs are selected
        setSelectedKPIs(selectedKPIs.filter(id => id !== kpiId));
      }
    } else if (selectedKPIs.length < 9) { // Maximum 9 KPIs
      setSelectedKPIs([...selectedKPIs, kpiId]);
    }
  };

  const renderKPIValue = (kpi: string, value: number) => {
    const formatType = kpi === 'spend' || kpi === 'sales' ? 'currency' :
                      kpi === 'impressions' || kpi === 'clicks' || kpi === 'orders' ? 'number' :
                      'decimal';
    
    const getStatusEmoji = (kpi: string, value: number) => {
      if (value === 0) return '⚪️';
      switch (kpi) {
        case 'ctr':
          return value > 1 ? '🟢' : value > 0.5 ? '🟡' : '🔴';
        case 'acos':
          return value < 30 ? '🟢' : value < 50 ? '🟡' : '🔴';
        case 'roas':
          return value > 3 ? '🟢' : value > 2 ? '🟡' : '🔴';
        case 'cvr':
          return value > 15 ? '🟢' : value > 10 ? '🟡' : '🔴';
        default:
          return '';
      }
    };

    const formattedValue = formatNumber(value, formatType);
    const emoji = getStatusEmoji(kpi, value);
    const needsPercentage = ['ctr', 'acos', 'cvr'].includes(kpi);
    
    return (
      <div className="text-lg font-semibold flex items-center">
        {emoji && <span className="mr-1">{emoji}</span>}
        {formattedValue}{needsPercentage ? '%' : ''}
      </div>
    );
  };

  const [isKPISettingsOpen, setIsKPISettingsOpen] = useState(false);

  const getOpportunities = (data: any[]) => {
    // First aggregate by campaign name using case-insensitive matching
    const groupedData = data.reduce((acc: { [key: string]: any }, item: any) => {
      const campaignName = (item.campaignName || '').trim();
      const key = campaignName.toLowerCase(); // Use lowercase key for case-insensitive matching
      
      if (!acc[key]) {
        acc[key] = {
          campaignName, // Keep original case for display
          spend: 0,
          sales: 0,
          orders: 0,
          clicks: 0,
          impressions: 0
        };
      }
      
      // Sum up all metrics
      acc[key].spend += Number(item.spend) || 0;
      acc[key].sales += Number(item.sevenDayTotalSales) || 0;
      acc[key].orders += Number(item.sevenDayTotalOrders) || 0;
      acc[key].clicks += Number(item.clicks) || 0;
      acc[key].impressions += Number(item.impressions) || 0;
      
      return acc;
    }, {});

    // Convert to array and calculate metrics
    const opportunities = Object.values(groupedData)
      .map((campaign: any) => {
        const acos = campaign.sales > 0 ? (campaign.spend / campaign.sales) * 100 : 0;
        const roas = campaign.spend > 0 ? campaign.sales / campaign.spend : 0;
        const ctr = campaign.impressions > 0 ? (campaign.clicks / campaign.impressions) * 100 : 0;
        const cvr = campaign.clicks > 0 ? (campaign.orders / campaign.clicks) * 100 : 0;
        
        return {
          ...campaign,
          acos,
          roas,
          ctr,
          cvr
        };
      })
      .filter(campaign => campaign.spend > 100 && campaign.acos > 50)
      .sort((a, b) => b.spend - a.spend)
      .slice(0, 5);

    return opportunities;
  };

  const renderOpportunities = () => {
    const opportunities = getOpportunities(filteredData || []);
    
    return (
      <div className="opportunities-section mt-4">
        <h3>Top 5 High-Spend, High-ACoS Campaigns</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead>
              <tr>
                <th className="px-4 py-2">Campaign</th>
                <th className="px-4 py-2">Spend</th>
                <th className="px-4 py-2">Sales</th>
                <th className="px-4 py-2">ACoS</th>
                <th className="px-4 py-2">ROAS</th>
                <th className="px-4 py-2">CTR</th>
                <th className="px-4 py-2">CVR</th>
              </tr>
            </thead>
            <tbody>
              {opportunities.map((campaign, index) => (
                <tr key={index} className="hover:bg-gray-100">
                  <td className="px-4 py-2">{campaign.campaignName}</td>
                  <td className="px-4 py-2">${campaign.spend.toFixed(2)}</td>
                  <td className="px-4 py-2">${campaign.sales.toFixed(2)}</td>
                  <td className="px-4 py-2">{campaign.acos.toFixed(1)}%</td>
                  <td className="px-4 py-2">{campaign.roas.toFixed(2)}</td>
                  <td className="px-4 py-2">{campaign.ctr.toFixed(1)}%</td>
                  <td className="px-4 py-2">{campaign.cvr.toFixed(1)}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Active Filters Banner */}
      <div className="bg-white rounded-lg shadow p-4 mb-4">
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex-1 min-w-[200px]">
            <input
              type="text"
              value={searchTermFilters.campaignName}
              onChange={(e) => handleCampaignInputChange(e.target.value)}
              placeholder="Filter by campaign name or keyword..."
              className="w-full px-3 py-2 border rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
          <div className="flex flex-wrap gap-2 items-center">
            {getActiveFilters().map(filter => (
              <span
                key={filter.key}
                className="inline-flex items-center bg-blue-50 px-2 py-1 rounded-md text-sm"
              >
                {filter.label}: {filter.value}
                <button
                  onClick={() => handleRemoveFilter(filter.key)}
                  className="ml-2 text-blue-500 hover:text-blue-700"
                >
                  ×
                </button>
              </span>
            ))}
            {(getActiveFilters().length > 0 || searchTermFilters.campaignName) && (
              <button
                onClick={clearAllFilters}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                Clear All
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Filter Section - More compact */}
      <div className="mb-4">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          {filterInputs.map((filter, index) => (
            <div key={index}>
              <input
                type="number"
                placeholder={filter.placeholder}
                value={searchTermFilters[filter.value] || ''}
                onChange={(e) => handleSearchTermFilterChange(filter.value, e.target.value)}
                className="w-full p-1.5 text-sm border rounded bg-gray-50 hover:bg-white focus:bg-white
                 border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500
                 placeholder-gray-400 transition-colors duration-200"
                min="0"
              />
            </div>
          ))}
        </div>
      </div>

      {/* KPI Settings */}
      <div className="mb-4 flex justify-end">
        <div className="relative">
          <button
            onClick={() => setIsKPISettingsOpen(!isKPISettingsOpen)}
            className="px-3 py-1.5 text-sm bg-white border rounded-md shadow hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            ⚙️ Customize KPIs
          </button>
          {isKPISettingsOpen && (
            <div className="absolute right-0 mt-2 w-64 bg-white border rounded-lg shadow-lg z-10 p-4">
              <div className="text-sm font-medium mb-2">Select KPIs (4-9)</div>
              <div className="space-y-2">
                {availableKPIs.map(kpi => (
                  <label key={kpi.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedKPIs.includes(kpi.id)}
                      onChange={() => handleKPIToggle(kpi.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span>{kpi.icon} {kpi.label}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* KPI row above charts */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-9 gap-3 mb-4 bg-white p-4 rounded-lg shadow">
        {selectedKPIs.map(kpiId => {
          const kpi = availableKPIs.find(k => k.id === kpiId)!;
          return (
            <div key={kpiId}>
              <div className="text-xs text-gray-500">{kpi.label}</div>
              {renderKPIValue(kpiId, overallMetrics[kpiId])}
            </div>
          );
        })}
      </div>

      {/* Charts section with filter controls */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">Search Term Performance</h2>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-500">
              {filteredData.length} {filteredData.length === 1 ? 'search term' : 'search terms'} found
            </span>
            {(getActiveFilters().length > 0 || searchTermFilters.campaignName) && (
              <button
                onClick={clearAllFilters}
                className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Clear All Filters
              </button>
            )}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg shadow" style={{ height: '400px' }}>
            <Chart 
              type="bar" 
              data={performanceChartData} 
              options={performanceChartOptions}
            />
          </div>
          <div className="bg-white p-4 rounded-lg shadow" style={{ height: '400px' }}>
            <Chart 
              type="bubble" 
              data={scatterChartData} 
              options={scatterChartOptions}
            />
          </div>
        </div>
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">Sort by:</label>
              <select
                value={sortField}
                onChange={(e) => setSortField(e.target.value as MetricType)}
                className="p-1.5 text-sm border rounded bg-gray-50 hover:bg-white focus:bg-white
                 border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              >
                <option value="customerSearchTerm">Search Term</option>
                <option value="campaignName">Campaign</option>
                <option value="spend">Spend</option>
                <option value="sales">Sales</option>
                <option value="acos">ACoS</option>
                <option value="roas">ROAS</option>
                <option value="orders">Orders</option>
                <option value="cvr">CVR</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">Order:</label>
              <select
                value={sortDirection}
                onChange={(e) => setSortDirection(e.target.value as 'asc' | 'desc')}
                className="p-1.5 text-sm border rounded bg-gray-50 hover:bg-white focus:bg-white
                 border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Search Term List */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full table-auto">
            <thead>
              <tr className="bg-gray-50">
                <th className="p-3 text-left text-sm font-semibold text-gray-600">
                  Search Term
                </th>
                <th className="p-3 text-left text-sm font-semibold text-gray-600">
                  Campaign
                </th>
                {selectedKPIs.map((kpi) => (
                  <th key={kpi} className="p-3 text-left text-sm font-semibold text-gray-600">
                    {kpi.toUpperCase()}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {paginatedData.map((term, index) => (
                <tr 
                  key={index} 
                  className="hover:bg-gray-50 border-t"
                >
                  <td className="p-3 text-left whitespace-nowrap">
                    <div className="text-sm">{term.customerSearchTerm}</div>
                  </td>
                  <td className="p-3 text-left whitespace-nowrap">
                    <div className="text-sm">{term.campaignName}</div>
                  </td>
                  {selectedKPIs.map((kpi) => (
                    <td key={kpi} className="p-3 text-left whitespace-nowrap">
                      <div className="text-sm">
                        {kpi.toLowerCase() === 'acos' ? (
                          <>
                            {term.acos === 0 ? '⚪️' : 
                             term.acos < 30 ? '🟢' : 
                             term.acos < 50 ? '🟡' : '🔴'} {formatNumber(term.acos, 'decimal')}%
                          </>
                        ) : kpi.toLowerCase() === 'roas' ? (
                          <>
                            {term.roas === 0 ? '⚪️' : 
                             term.roas > 3 ? '🟢' : 
                             term.roas > 2 ? '🟡' : '🔴'} {formatNumber(term.roas, 'decimal')}
                          </>
                        ) : kpi.toLowerCase() === 'cvr' ? (
                          <>
                            {term.cvr === 0 ? '⚪️' : 
                             term.cvr > 15 ? '🟢' : 
                             term.cvr > 10 ? '🟡' : '🔴'} {formatNumber(term.cvr, 'decimal')}%
                          </>
                        ) : kpi.toLowerCase() === 'ctr' ? (
                          <>{formatNumber(term[kpi.toLowerCase()], 'decimal')}%</>
                        ) : kpi.toLowerCase() === 'spend' || kpi.toLowerCase() === 'sales' ? (
                          <>{formatNumber(term[kpi.toLowerCase()], 'currency')}</>
                        ) : (
                          <>{formatNumber(term[kpi.toLowerCase()], 'number')}</>
                        )}
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination at bottom */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t">
          <div className="text-sm text-gray-500">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredData.length)} of {filteredData.length} results
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border rounded hover:bg-gray-100 disabled:opacity-50"
            >
              Previous
            </button>
            <span className="text-gray-600">
              Page {currentPage} of {Math.ceil(filteredData.length / itemsPerPage)}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(Math.ceil(filteredData.length / itemsPerPage), prev + 1))}
              disabled={currentPage >= Math.ceil(filteredData.length / itemsPerPage)}
              className="px-3 py-1 border rounded hover:bg-gray-100 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CampaignAnalysis;
