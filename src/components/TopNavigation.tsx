import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setActiveView } from '../redux/campaignSlice';
import { Link } from 'react-router-dom';
import { RootState } from '../redux/store';
import UploadButton from './UploadButton';

interface Props {
  activeView: string;
}

const TopNavigation: React.FC<Props> = ({ activeView }) => {
  const dispatch = useDispatch();
  const { data, activeReportType } = useSelector((state: RootState) => state.campaign);

  const handleViewChange = (view: string) => {
    dispatch(setActiveView(view));
  };

  // Get the portfolio name from the first row of data
  const portfolioName = 'Amazon PPC';

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 font-bold text-xl text-gray-800 mr-8">
              {portfolioName}
            </div>
            <div className="hidden sm:flex sm:space-x-8">
              <Link 
                to="/"
                onClick={() => handleViewChange('dashboard')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeView === 'dashboard' ? 'bg-gray-900 text-white' : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                Dashboard
              </Link>
              <Link
                to="/campaigns"
                onClick={() => handleViewChange('campaigns')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeView === 'campaigns' ? 'bg-gray-900 text-white' : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                Campaigns
              </Link>
              <Link
                to="/search-terms"
                onClick={() => handleViewChange('searchTerms')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeView === 'searchTerms' ? 'bg-gray-900 text-white' : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                Search Terms
              </Link>
              <Link
                to="/wasted-spend"
                onClick={() => handleViewChange('wastedSpend')}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  activeView === 'wastedSpend' ? 'bg-gray-900 text-white' : 'text-gray-700 hover:text-gray-900'
                }`}
              >
                Wasted Spend
              </Link>
            </div>
          </div>
          <div className="flex items-center">
            <UploadButton />
          </div>
        </div>
      </div>
    </nav>
  );
};

export default TopNavigation;
