import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { markCloseIQPopupShown } from '../store/campaignSlice';

const CloseIQPopup: React.FC = () => {
  const dispatch = useDispatch();
  const isVisible = useSelector((state: RootState) => state.campaign.isCloseIQPopupVisible);
  
  const closePopup = () => {
    dispatch(markCloseIQPopupShown());
  };
  
  const handleLearnMore = () => {
    window.open('https://closeiq.sellersynapse.com/', '_blank', 'noopener,noreferrer');
    dispatch(markCloseIQPopupShown());
  };
  
  if (!isVisible) return null;
  
  return (
    <>
      {/* Background overlay for mobile */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-25 z-40 sm:hidden"
        onClick={closePopup}
      />
      
      {/* Popup container */}
      <div className={`
        fixed z-50 transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'}
        
        /* Mobile styles */
        bottom-4 left-4 right-4 mx-auto
        
        /* Tablet and up */
        sm:bottom-4 sm:right-4 sm:left-auto sm:w-80
        
        /* Large screens */
        lg:w-80
      `}>
        <div className="bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden">
          {/* Header with close button */}
          <div className="relative px-6 pt-4">
            <button
              onClick={closePopup}
              className="absolute top-2 right-2 p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-full transition-colors duration-200"
              aria-label="Close popup"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Content */}
          <div className="px-6 pb-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                Want your own branded version of this tool?
              </h3>
              <p className="text-base text-gray-600 mb-6">
                Check out CloseIQ today!
              </p>
              
              {/* Learn More Button */}
              <button
                onClick={handleLearnMore}
                className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-gradient-to-r from-gray-800 via-purple-800 to-violet-800 hover:from-gray-900 hover:via-purple-900 hover:to-violet-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-105"
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CloseIQPopup;
