import React, { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer
} from 'recharts';
import { CampaignPerformance } from '../types/campaign';

const WastedSpendView: React.FC = () => {
  const { data, loading, error } = useSelector((state: RootState) => state.campaign);
  const [expandedCampaign, setExpandedCampaign] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    minSpend: 0,
    minClicks: 0,
    campaignType: 'all'
  });

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Add loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-600">Loading campaign data...</div>
      </div>
    );
  }

  // Add error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-600">Error: {error}</div>
      </div>
    );
  }

  // Add empty data state
  if (!data?.searchTerm?.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-600">No campaign data available. Please upload a report.</div>
      </div>
    );
  }

  // Filter wasted spend campaigns
  const wastedSpendCampaigns = useMemo(() => {
    console.log('WastedSpendView - Initial data:', {
      hasData: !!data,
      searchTermData: data?.searchTerm?.length,
      sampleData: data?.searchTerm?.[0]
    });

    if (!data || !data.searchTerm) return [];

    // First, aggregate campaign data
    const campaignMap = new Map<string, CampaignPerformance>();
    
    data.searchTerm.forEach((item: any) => {
      if (!item.campaignName) {
        console.log('Skipping item without campaign name:', item);
        return;
      }

      const existing = campaignMap.get(item.campaignName) || {
        name: item.campaignName,
        impressions: 0,
        clicks: 0,
        spend: 0,
        sevenDayTotalSales: 0,
        sevenDayTotalOrders: 0,
        acos: 0,
        roas: 0
      };

      // Log the values before aggregation
      console.log('Processing campaign data:', {
        campaignName: item.campaignName,
        currentSpend: existing.spend,
        addingSpend: Number(item.spend) || 0,
        currentSales: existing.sevenDayTotalSales,
        addingSales: Number(item.sevenDayTotalSales) || 0
      });

      existing.impressions += Number(item.impressions) || 0;
      existing.clicks += Number(item.clicks) || 0;
      existing.spend += Number(item.spend) || 0;
      existing.sevenDayTotalSales += Number(item.sevenDayTotalSales) || 0;
      existing.sevenDayTotalOrders += Number(item.sevenDayTotalOrders) || 0;

      campaignMap.set(item.campaignName, existing);
    });

    // Log the final campaign map
    console.log('Campaign map after processing:', {
      totalCampaigns: campaignMap.size,
      campaigns: Array.from(campaignMap.entries()).map(([name, data]) => ({
        name,
        spend: data.spend,
        sales: data.sevenDayTotalSales
      }))
    });

    // Then filter and sort campaigns
    return Array.from(campaignMap.values())
      .filter((campaign: CampaignPerformance) => {
        const targeting = (data.searchTerm.find((item: any) => item.campaignName === campaign.name)?.targeting) || 'unknown';
        return campaign.spend > 0 && 
               campaign.sevenDayTotalSales === 0 &&
               campaign.spend >= filters.minSpend &&
               campaign.clicks >= filters.minClicks &&
               (filters.campaignType === 'all' || targeting === filters.campaignType);
      })
      .sort((a: CampaignPerformance, b: CampaignPerformance) => b.spend - a.spend);
  }, [data, filters]);

  // Pagination
  const totalPages = Math.ceil(wastedSpendCampaigns.length / itemsPerPage);
  const paginatedCampaigns = wastedSpendCampaigns.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const chartData = paginatedCampaigns.map((campaign: CampaignPerformance) => ({
    name: campaign.name,
    spend: campaign.spend,
  }));

  // Get keyword stats for a campaign
  const getKeywordStats = (campaignName: string) => {
    if (!data || !data.searchTerm) return [];
    
    return data.searchTerm
      .filter((item: any) => item.campaignName === campaignName)
      .map((item: any) => ({
        keyword: item.customerSearchTerm || 'N/A',
        spend: Number(item.spend) || 0,
        clicks: Number(item.clicks) || 0,
        impressions: Number(item.impressions) || 0
      }))
      .sort((a, b) => b.spend - a.spend);
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Wasted Spend Analysis</h2>
        
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Minimum Spend</label>
            <input
              type="number"
              min="0"
              value={filters.minSpend}
              onChange={(e) => setFilters({ ...filters, minSpend: Number(e.target.value) })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Minimum Clicks</label>
            <input
              type="number"
              min="0"
              value={filters.minClicks}
              onChange={(e) => setFilters({ ...filters, minClicks: Number(e.target.value) })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Campaign Type</label>
            <select
              value={filters.campaignType}
              onChange={(e) => setFilters({ ...filters, campaignType: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="all">All Types</option>
              <option value="auto">Auto</option>
              <option value="manual">Manual</option>
            </select>
          </div>
        </div>

        {/* Chart */}
        <div className="h-64 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="spend" fill="#EF4444" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Table */}
        <div className="mt-8">
          <div className="flex flex-col">
            <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Campaign Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Spend
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Clicks
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          CTR
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {paginatedCampaigns.map((campaign: CampaignPerformance) => {
                        const ctr = campaign.impressions > 0 ? campaign.clicks / campaign.impressions : 0;
                        return (
                          <React.Fragment key={campaign.name}>
                            <tr className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {campaign.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${campaign.spend.toFixed(2)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {campaign.clicks}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {(ctr * 100).toFixed(2)}%
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <button
                                  onClick={() => setExpandedCampaign(
                                    expandedCampaign === campaign.name ? null : campaign.name
                                  )}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  {expandedCampaign === campaign.name ? 'Hide Details' : 'Show Details'}
                                </button>
                              </td>
                            </tr>
                            {expandedCampaign === campaign.name && (
                              <tr>
                                <td colSpan={5} className="px-6 py-4 bg-gray-50">
                                  <div className="space-y-4">
                                    <h4 className="text-sm font-medium text-gray-900">Search Terms</h4>
                                    <div className="shadow overflow-hidden border border-gray-200 sm:rounded-lg">
                                      <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                          <tr>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                              Search Term
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                              Spend
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                              Clicks
                                            </th>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                              Impressions
                                            </th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {getKeywordStats(campaign.name).map((keyword, idx) => (
                                            <tr key={idx} className="hover:bg-gray-100">
                                              <td className="px-4 py-2 text-sm text-gray-900">{keyword.keyword}</td>
                                              <td className="px-4 py-2 text-sm text-gray-500">${keyword.spend.toFixed(2)}</td>
                                              <td className="px-4 py-2 text-sm text-gray-500">{keyword.clicks}</td>
                                              <td className="px-4 py-2 text-sm text-gray-500">{keyword.impressions}</td>
                                            </tr>
                                          ))}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex justify-between items-center">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default WastedSpendView;
