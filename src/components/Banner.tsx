import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { setBannerVisibility } from '../store/campaignSlice';

const Banner: React.FC = () => {
  const dispatch = useDispatch();
  const isVisible = useSelector((state: RootState) => state.campaign.isBannerVisible);
  
  const closeBanner = () => {
    dispatch(setBannerVisibility(false));
  };
  
  if (!isVisible) return null;
  
  return (
    <div className="relative bg-gradient-to-r from-secondary-dark to-indigo-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 flex items-center justify-center">
        <div className="flex items-center flex-wrap">
          <span className="text-sm sm:text-base font-medium text-white">
            ATTENTION AMAZON AGENCIES: Transform PPC audits from hours to minutes with white-label
          </span>
          <span className="text-sm sm:text-base font-medium text-white ml-1">
            reports
          </span>
          <a
            href="https://closeiq.sellersynapse.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-secondary-dark bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-secondary-dark focus:ring-white ml-2"
          >
            Watch Demo
          </a>
        </div>
        <button
          onClick={closeBanner}
          className="absolute right-4 flex-shrink-0 p-1 text-white hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white"
        >
          <span className="sr-only">Close</span>
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default Banner;
