import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../store';

interface Recommendation {
  id: string;
  type: string;
  priority: 'high' | 'medium' | 'low';
  message: string;
  metrics: {
    current: number;
    target: number;
    unit: string;
  };
  campaign: string;
  action: string;
}

const Recommendations: React.FC = () => {
  const { recommendations } = useSelector((state: RootState) => state.campaign);

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionIcon = (type: string): string => {
    switch (type) {
      case 'optimization':
        return '⚡️';
      case 'targeting':
        return '🎯';
      case 'conversion':
        return '💰';
      case 'performance':
        return '📊';
      default:
        return '💡';
    }
  };

  if (!recommendations?.length) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Optimization Opportunities</h2>
        <p className="mt-2 text-sm text-gray-600">
          Actionable insights to improve your campaign performance
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {recommendations.map((rec) => (
          <div
            key={rec.id}
            className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-xl" role="img" aria-label="action type">
                    {getActionIcon(rec.type)}
                  </span>
                  <span
                    className={`px-2.5 py-0.5 rounded-full text-xs font-medium uppercase ${getPriorityColor(
                      rec.priority
                    )}`}
                  >
                    {rec.priority} Priority
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{rec.message}</h3>
                <div className="text-sm text-gray-600 space-y-2">
                  <p className="font-medium">Recommended Action:</p>
                  <p>{rec.action}</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Recommendations;
