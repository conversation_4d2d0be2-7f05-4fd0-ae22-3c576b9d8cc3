import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { setDateRange } from '../store/campaignSlice';

const DateRangeSelector: React.FC = () => {
  const dispatch = useDispatch();
  const { data, startDate, endDate } = useSelector((state: RootState) => state.campaign);
  const [daysBack, setDaysBack] = useState<number>(7);

  useEffect(() => {
    // When data is loaded, set initial dates
    if (data.searchTerm.length > 0) {
      const dates = data.searchTerm
        .map(item => new Date(item.startDate))
        .sort((a, b) => a.getTime() - b.getTime());

      if (dates.length > 0) {
        const latestDate = dates[dates.length - 1];
        const earliestDate = dates[0];
        
        // Calculate default days back
        const daysDiff = Math.ceil((latestDate.getTime() - earliestDate.getTime()) / (1000 * 60 * 60 * 24));
        setDaysBack(daysDiff);

        // Set the date range
        dispatch(setDateRange({
          startDate: earliestDate.toISOString().split('T')[0],
          endDate: latestDate.toISOString().split('T')[0]
        }));
      }
    }
  }, [data.searchTerm, dispatch]);

  const handleDaysChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const days = parseInt(event.target.value);
    setDaysBack(days);

    // Calculate new start date based on end date
    const end = new Date(endDate || new Date());
    const start = new Date(end);
    start.setDate(end.getDate() - days);

    dispatch(setDateRange({
      startDate: start.toISOString().split('T')[0],
      endDate: end.toISOString().split('T')[0]
    }));
  };

  if (!startDate || !endDate) return null;

  return (
    <div className="flex items-center space-x-4 mb-4 bg-white p-4 rounded-lg shadow">
      <div className="flex items-center space-x-2">
        <label htmlFor="daysBack" className="text-sm font-medium text-gray-700">
          Show data for the last:
        </label>
        <select
          id="daysBack"
          value={daysBack}
          onChange={handleDaysChange}
          className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        >
          <option value={7}>7 days</option>
          <option value={14}>14 days</option>
          <option value={30}>30 days</option>
          <option value={60}>60 days</option>
          <option value={90}>90 days</option>
        </select>
      </div>
      <div className="text-sm text-gray-500">
        {startDate} to {endDate}
      </div>
    </div>
  );
};

export default DateRangeSelector;
