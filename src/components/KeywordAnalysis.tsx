import React, { useState, useMemo } from 'react';
import { BaseReport } from '../types/reports';

interface KeywordAnalysisProps {
  data: BaseReport[];
  filters: {
    startDate: Date | null;
    endDate: Date | null;
    campaigns: string[];
  };
}

interface KeywordMetrics {
  keyword: string;
  impressions: number;
  clicks: number;
  spend: number;
  sales: number;
  orders: number;
  acos: number;
  ctr: number;
  conversionRate: number;
  campaigns: Set<string>;
}

const KeywordAnalysis: React.FC<KeywordAnalysisProps> = ({ data, filters }) => {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof KeywordMetrics;
    direction: 'asc' | 'desc';
  }>({ key: 'spend', direction: 'desc' });

  const [searchTerm, setSearchTerm] = useState('');

  const keywordMetrics = useMemo(() => {
    const metrics = new Map<string, KeywordMetrics>();

    data.forEach(item => {
      if (!item.customerSearchTerm) return;

      const keyword = item.customerSearchTerm.toLowerCase();
      const existing = metrics.get(keyword) || {
        keyword: item.customerSearchTerm,
        impressions: 0,
        clicks: 0,
        spend: 0,
        sales: 0,
        orders: 0,
        acos: 0,
        ctr: 0,
        conversionRate: 0,
        campaigns: new Set<string>()
      };

      existing.impressions += Number(item.impressions) || 0;
      existing.clicks += Number(item.clicks) || 0;
      existing.spend += Number(item.spend) || 0;
      existing.sales += Number(item.sevenDayTotalSales) || 0;
      existing.orders += Number(item.sevenDayTotalUnits) || 0;
      existing.campaigns.add(item.campaignName);

      // Calculate derived metrics
      existing.acos = existing.sales > 0 ? (existing.spend / existing.sales) * 100 : Infinity;
      existing.ctr = existing.impressions > 0 ? (existing.clicks / existing.impressions) * 100 : 0;
      existing.conversionRate = existing.clicks > 0 ? (existing.orders / existing.clicks) * 100 : 0;

      metrics.set(keyword, existing);
    });

    return Array.from(metrics.values());
  }, [data]);

  const sortedKeywords = useMemo(() => {
    return [...keywordMetrics]
      .filter(keyword => 
        keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortConfig.direction === 'asc' 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        return 0;
      });
  }, [keywordMetrics, sortConfig, searchTerm]);

  const handleSort = (key: keyof KeywordMetrics) => {
    setSortConfig(current => ({
      key,
      direction: current.key === key && current.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const renderSortArrow = (key: keyof KeywordMetrics) => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Keyword Analysis</h2>
        <input
          type="text"
          placeholder="Search keywords..."
          className="px-4 py-2 border rounded-lg"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white rounded-lg shadow">
          <thead>
            <tr className="bg-gray-50">
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('keyword')}
              >
                Keyword {renderSortArrow('keyword')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('impressions')}
              >
                Impressions {renderSortArrow('impressions')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('clicks')}
              >
                Clicks {renderSortArrow('clicks')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('spend')}
              >
                Spend {renderSortArrow('spend')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('sales')}
              >
                Sales {renderSortArrow('sales')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('acos')}
              >
                ACoS {renderSortArrow('acos')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('ctr')}
              >
                CTR {renderSortArrow('ctr')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('conversionRate')}
              >
                Conv. Rate {renderSortArrow('conversionRate')}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {sortedKeywords.map((keyword, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {keyword.keyword}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {keyword.impressions.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {keyword.clicks.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${keyword.spend.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${keyword.sales.toFixed(2)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                  keyword.acos > 50 ? 'text-red-600' : 
                  keyword.acos > 30 ? 'text-yellow-600' : 
                  'text-green-600'
                }`}>
                  {keyword.acos === Infinity ? '∞' : `${keyword.acos.toFixed(1)}%`}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                  keyword.ctr > 1 ? 'text-green-600' : 
                  keyword.ctr > 0.5 ? 'text-yellow-600' : 
                  'text-gray-500'
                }`}>
                  {keyword.ctr.toFixed(2)}%
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                  keyword.conversionRate > 15 ? 'text-green-600' : 
                  keyword.conversionRate > 8 ? 'text-yellow-600' : 
                  'text-gray-500'
                }`}>
                  {keyword.conversionRate.toFixed(2)}%
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default KeywordAnalysis;
