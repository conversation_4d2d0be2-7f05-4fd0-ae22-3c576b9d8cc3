interface CampaignData {
  campaignName: string;
  adGroup: string;
  targetingType: string;
  matchType: string;
  target: string;
  impressions: number;
  clicks: number;
  spend: number;
  sales: number;
  acos: number;
  roas: number;
  ctr: number;
  cpc: number;
  cvr: number;
  orders: number;
  unitsSold: number;
  date: string;
}

export const parseCsvData = async (csvContent: string): Promise<CampaignData[]> => {
  const lines = csvContent.split('\n');
  const headers = lines[0].split(',').map(header => header.trim());
  
  const data: CampaignData[] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    const values = line.split(',').map(value => value.trim());
    const row: any = {};
    
    headers.forEach((header, index) => {
      const value = values[index];
      
      // Convert numeric values
      if (['impressions', 'clicks', 'orders', 'unitsSold'].includes(header)) {
        row[toCamelCase(header)] = parseInt(value, 10) || 0;
      } else if (['spend', 'sales', 'acos', 'roas', 'ctr', 'cpc', 'cvr'].includes(header)) {
        row[toCamelCase(header)] = parseFloat(value) || 0;
      } else {
        row[toCamelCase(header)] = value;
      }
    });
    
    data.push(row as CampaignData);
  }
  
  return data;
};

const toCamelCase = (str: string): string => {
  return str.toLowerCase()
    .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase());
};
