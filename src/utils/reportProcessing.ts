import { utils, read, WorkBook } from 'xlsx';
import {
  BaseReport,
  SearchTermReport,
  PlacementReport,
  TargetingReport,
  AdvertisedProductReport,
  ReportType,
  ProcessedReport
} from '../types/reports';

export const columnMappings = {
  campaignName: ['Campaign Name', 'Campaign', 'Campaign name'],
  impressions: ['Impressions'],
  clicks: ['Clicks'],
  spend: ['Spend', 'Cost'],
  sevenDayTotalSales: [
    '7 Day Total Sales',
    'Total Sales',
    '7 Day Total Orders Sales',
    '7 Day Total Orders',
    'Sales',
    '7 Day Advertised Product Sales',
    '7 Day Other Product Sales',
    'Total Advertising Cost of Sales (ACoS)'
  ],
  acos: [
    'Total Advertising Cost of Sales (ACOS)',
    'ACOS',
    'Advertising Cost of Sales (ACOS)',
    'Total Advertising Cost of Sales',
    'Advertising Cost of Sales',
    'ACoS'
  ],
  portfolioName: ['Portfolio name', 'Portfolio Name', 'Portfolio'],
  currency: ['Currency'],
  targeting: ['Targeting', 'Target'],
  matchType: ['Match Type'],
  customerSearchTerm: ['Customer Search Term', 'Search Term'],
  adGroupName: ['Ad Group Name', 'Ad Group'],
  sevenDayTotalUnits: [
    '7 Day Total Units (#)',
    '7 Day Total Units',
    'Total Units',
    'Units Sold'
  ],
  sevenDayAdvertisedSkuUnits: [
    '7 Day Advertised SKU Units (#)',
    '7 Day Advertised SKU Units',
    'Advertised SKU Units'
  ],
  sevenDayOtherSkuUnits: [
    '7 Day Other SKU Units (#)',
    '7 Day Other SKU Units',
    'Other SKU Units'
  ],
  sevenDayAdvertisedSkuSales: [
    '7 Day Advertised SKU Sales',
    'Advertised SKU Sales',
    '7 Day Advertised Product Sales'
  ],
  sevenDayOtherSkuSales: [
    '7 Day Other SKU Sales',
    'Other SKU Sales',
    '7 Day Other Product Sales'
  ]
};

function cleanNumericValue(value: any): number {
  console.log('Cleaning numeric value:', { raw: value, type: typeof value });

  if (value === null || value === undefined || value === '') {
    return 0;
  }

  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }

  let stringValue = String(value).trim();
  stringValue = stringValue.replace(/[$£€,]/g, '');

  if (stringValue.endsWith('%')) {
    stringValue = stringValue.slice(0, -1);
    const number = parseFloat(stringValue);
    return isNaN(number) ? 0 : number / 100;
  }

  const number = parseFloat(stringValue);

  console.log('Cleaned numeric value:', { 
    original: value,
    cleaned: stringValue,
    result: isNaN(number) ? 0 : number 
  });

  return isNaN(number) ? 0 : number;
}

function getColumnValue(row: any, columnKeys: string[]): any {
  console.log('Searching for column value:', { 
    searchKeys: columnKeys,
    availableKeys: Object.keys(row)
  });

  for (const key of columnKeys) {
    if (row[key] !== undefined) {
      console.log('Found exact match:', { key, value: row[key] });
      return row[key];
    }
  }

  const rowKeys = Object.keys(row);
  const lowerCaseKeys = rowKeys.map(k => k.toLowerCase());
  
  for (const searchKey of columnKeys) {
    const lowerSearchKey = searchKey.toLowerCase();
    const index = lowerCaseKeys.findIndex(k => k === lowerSearchKey);
    if (index !== -1) {
      console.log('Found case-insensitive match:', { 
        searchKey,
        matchedKey: rowKeys[index],
        value: row[rowKeys[index]]
      });
      return row[rowKeys[index]];
    }
  }

  for (const searchKey of columnKeys) {
    const lowerSearchKey = searchKey.toLowerCase();
    for (let i = 0; i < lowerCaseKeys.length; i++) {
      if (lowerCaseKeys[i].includes(lowerSearchKey) || 
          lowerSearchKey.includes(lowerCaseKeys[i])) {
        console.log('Found partial match:', { 
          searchKey,
          matchedKey: rowKeys[i],
          value: row[rowKeys[i]]
        });
        return row[rowKeys[i]];
      }
    }
  }

  console.log('No match found for:', columnKeys);
  return null;
}

function getBaseFields(row: any): BaseReport {
  console.log('Processing row:', row);

  // Get raw values first
  const rawValues = {
    clicks: getColumnValue(row, columnMappings.clicks),
    impressions: getColumnValue(row, columnMappings.impressions),
    spend: getColumnValue(row, columnMappings.spend),
    sales: getColumnValue(row, columnMappings.sevenDayTotalSales),
    units: getColumnValue(row, columnMappings.sevenDayTotalUnits),
    advertised_sales: getColumnValue(row, columnMappings.sevenDayAdvertisedSkuSales),
    other_sales: getColumnValue(row, columnMappings.sevenDayOtherSkuSales)
  };

  console.log('Raw values:', rawValues);

  // Clean numeric values
  const clicks = cleanNumericValue(rawValues.clicks);
  const impressions = cleanNumericValue(rawValues.impressions);
  const spend = cleanNumericValue(rawValues.spend);
  
  // For sales, try to get the total from different possible sources
  let sevenDayTotalSales = cleanNumericValue(rawValues.sales);
  if (sevenDayTotalSales === 0) {
    const advertisedSales = cleanNumericValue(rawValues.advertised_sales);
    const otherSales = cleanNumericValue(rawValues.other_sales);
    if (advertisedSales > 0 || otherSales > 0) {
      sevenDayTotalSales = advertisedSales + otherSales;
    }
  }

  const sevenDayTotalUnits = cleanNumericValue(rawValues.units);

  console.log('Cleaned numeric values:', {
    clicks,
    impressions,
    spend,
    sevenDayTotalSales,
    sevenDayTotalUnits
  });

  // Calculate derived metrics
  const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0;
  const cpc = clicks > 0 ? spend / clicks : 0;
  const acos = sevenDayTotalSales > 0 ? (spend / sevenDayTotalSales) * 100 : 0;
  const roas = spend > 0 ? sevenDayTotalSales / spend : 0;
  const sevenDayConversionRate = clicks > 0 ? (sevenDayTotalUnits / clicks) * 100 : 0;

  console.log('Calculated metrics:', {
    ctr,
    cpc,
    acos,
    roas,
    sevenDayConversionRate
  });

  const base: BaseReport = {
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    campaignName: getColumnValue(row, columnMappings.campaignName) || '',
    impressions,
    clicks,
    ctr,
    cpc,
    spend,
    sevenDayTotalSales,
    acos,
    roas,
    portfolioName: getColumnValue(row, columnMappings.portfolioName) || '',
    currency: getColumnValue(row, columnMappings.currency) || 'USD',
    targeting: getColumnValue(row, columnMappings.targeting) || '',
    matchType: getColumnValue(row, columnMappings.matchType) || '',
    customerSearchTerm: getColumnValue(row, columnMappings.customerSearchTerm) || '',
    adGroupName: getColumnValue(row, columnMappings.adGroupName) || '',
    sevenDayTotalOrders: sevenDayTotalUnits,
    sevenDayTotalUnits,
    sevenDayConversionRate,
    sevenDayAdvertisedSkuUnits: cleanNumericValue(getColumnValue(row, columnMappings.sevenDayAdvertisedSkuUnits)),
    sevenDayOtherSkuUnits: cleanNumericValue(getColumnValue(row, columnMappings.sevenDayOtherSkuUnits)),
    sevenDayAdvertisedSkuSales: cleanNumericValue(getColumnValue(row, columnMappings.sevenDayAdvertisedSkuSales)),
    sevenDayOtherSkuSales: cleanNumericValue(getColumnValue(row, columnMappings.sevenDayOtherSkuSales))
  };

  console.log('Final base fields:', base);

  return base;
}

function determineReportType(data: BaseReport[]): ReportType {
  if (data.length === 0) return 'searchTerm';
  
  const firstRow = data[0];
  
  if (firstRow.customerSearchTerm && firstRow.matchType) {
    return 'searchTerm';
  }
  
  if (firstRow.targeting && firstRow.targeting.toLowerCase().includes('placement')) {
    return 'placement';
  }
  
  if (firstRow.targeting && !firstRow.customerSearchTerm) {
    return 'targeting';
  }
  
  if (firstRow.sevenDayAdvertisedSkuSales !== undefined && 
      firstRow.sevenDayAdvertisedSkuUnits !== undefined) {
    return 'advertisedProduct';
  }
  
  return 'searchTerm';
}

function extractDateFromFilename(filename: string): string | null {
  const datePatterns = [
    /(\d{4}-\d{2}-\d{2})/,           // YYYY-MM-DD
    /(\d{2}-\d{2}-\d{4})/,           // DD-MM-YYYY or MM-DD-YYYY
    /(\d{8})/,                        // YYYYMMDD
    /(\d{2}\.\d{2}\.\d{4})/          // DD.MM.YYYY
  ];

  for (const pattern of datePatterns) {
    const match = filename.match(pattern);
    if (match) {
      return match[1];
    }
  }
  return null;
}

export function processReport(file: File): Promise<ProcessedReport> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = read(data, { 
          type: 'array',
          cellDates: true,
          cellNF: false,
          cellText: false,
          raw: true
        });

        if (!workbook.SheetNames.length) {
          throw new Error('No sheets found in the workbook');
        }

        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        if (!firstSheet['!ref']) {
          throw new Error('Sheet is empty');
        }

        const jsonData = utils.sheet_to_json(firstSheet, {
          raw: true,
          defval: null,
          blankrows: false
        });

        if (!Array.isArray(jsonData) || jsonData.length < 2) {
          throw new Error('No valid data rows found in the file');
        }

        const reportDate = extractDateFromFilename(file.name) || 
                          new Date().toISOString().split('T')[0];

        const processedData = jsonData.map((row: any) => {
          const baseFields = getBaseFields(row);
          return {
            ...baseFields,
            startDate: reportDate,
            endDate: reportDate
          };
        }).filter(row => {
          return row.campaignName && 
                 (row.impressions > 0 || row.clicks > 0 || row.spend > 0 || row.sevenDayTotalSales > 0);
        });

        if (processedData.length === 0) {
          throw new Error('No valid campaign data found after processing');
        }

        console.log('Processed report data:', {
          rowCount: processedData.length,
          sampleRow: processedData[0],
          reportDate,
          filename: file.name
        });

        const reportType = determineReportType(processedData);
        resolve({
          type: reportType,
          data: processedData
        });
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read the file'));
    };

    reader.readAsArrayBuffer(file);
  });
}
