# Amazon PPC Campaign Analyzer

A powerful React-based web application for analyzing Amazon PPC campaign data, providing actionable insights and optimization recommendations.

## Features

- CSV data import with drag-and-drop functionality
- Comprehensive campaign performance analysis
- Interactive data visualizations
- Automated optimization recommendations
- Performance metrics tracking
- Export capabilities for reports and findings

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
```bash
git clone [repository-url]
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

## Usage

1. Upload your Amazon PPC campaign data CSV file
2. View automatically generated analysis and insights
3. Explore optimization recommendations
4. Export findings and action items

## Tech Stack

- React with TypeScript
- Redux Toolkit for state management
- Recharts for data visualization
- Tailwind CSS for styling
- React Dropzone for file uploads

## Project Structure

```
amazon-ppc-analyzer/
├── src/
│   ├── components/        # React components
│   ├── store/            # Redux store configuration
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── services/         # API and data processing services
│   └── styles/           # CSS and Tailwind configurations
├── public/
└── package.json
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## License

This project is licensed under the MIT License.
